#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
印版尺寸计算器测试脚本
"""

import requests
import json
import sys

def test_api():
    """测试API功能"""
    base_url = "http://127.0.0.1:5000"
    
    print("=" * 60)
    print("印版尺寸反向计算推理系统 - 功能测试")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        {
            "name": "A4宣传单",
            "data": {
                "finished_width": 210,
                "finished_height": 297,
                "bleed": 3,
                "safety_margin": 5,
                "copies_horizontal": 2,
                "copies_vertical": 2,
                "gripper_edge": "long"
            }
        },
        {
            "name": "名片",
            "data": {
                "finished_width": 90,
                "finished_height": 54,
                "bleed": 2,
                "safety_margin": 5,
                "copies_horizontal": 4,
                "copies_vertical": 6,
                "gripper_edge": "short"
            }
        },
        {
            "name": "海报",
            "data": {
                "finished_width": 420,
                "finished_height": 594,
                "bleed": 5,
                "safety_margin": 10,
                "copies_horizontal": 1,
                "copies_vertical": 1,
                "gripper_edge": "long"
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {test_case['name']}")
        print("-" * 40)
        
        try:
            response = requests.post(
                f"{base_url}/api/calculate",
                json=test_case['data'],
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    data = result['data']
                    
                    print(f"✓ 计算成功")
                    print(f"  输入: {data['input_parameters']['finished_size']}")
                    print(f"  拼版: {data['input_parameters']['layout']}")
                    print(f"  印版尺寸: {data['calculated_results']['required_plate_size']}")
                    print(f"  材料利用率: {data['material_efficiency']}")
                    print(f"  适用印刷机: {', '.join(data['suitable_presses']) if data['suitable_presses'] else '无'}")
                    
                    # 显示最优方案
                    if data['optimal_layouts']:
                        print(f"  推荐方案: {data['optimal_layouts'][0]['layout']} "
                              f"({data['optimal_layouts'][0]['efficiency']})")
                    
                    # 显示成本
                    cost = data['cost_analysis']
                    print(f"  预估成本: ¥{cost['total_cost']} (单件: ¥{cost['cost_per_copy']})")
                    
                else:
                    print(f"✗ 计算失败: {result.get('error', '未知错误')}")
            else:
                print(f"✗ 请求失败: HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"✗ 网络错误: {e}")
        except Exception as e:
            print(f"✗ 未知错误: {e}")
    
    # 测试API端点
    print(f"\n\n测试API端点")
    print("-" * 40)
    
    try:
        # 测试纸张尺寸API
        response = requests.get(f"{base_url}/api/paper-sizes", timeout=5)
        if response.status_code == 200:
            paper_sizes = response.json()
            print(f"✓ 纸张尺寸API: 获取到 {len(paper_sizes)} 种标准尺寸")
        else:
            print(f"✗ 纸张尺寸API失败: HTTP {response.status_code}")
            
        # 测试印刷机规格API
        response = requests.get(f"{base_url}/api/press-sizes", timeout=5)
        if response.status_code == 200:
            press_sizes = response.json()
            print(f"✓ 印刷机规格API: 获取到 {len(press_sizes)} 种印刷机规格")
        else:
            print(f"✗ 印刷机规格API失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"✗ API测试错误: {e}")
    
    print(f"\n\n测试完成！")
    print(f"访问 {base_url} 查看Web界面")
    print("=" * 60)

def test_calculation_logic():
    """测试计算逻辑"""
    from app import PlateCalculator
    
    print("\n\n计算逻辑单元测试")
    print("-" * 40)
    
    calculator = PlateCalculator()
    
    # 测试基本计算
    result = calculator.calculate_plate_size(
        finished_width=210,
        finished_height=297,
        bleed=3,
        safety_margin=5,
        copies_horizontal=1,
        copies_vertical=1,
        gripper_edge="long"
    )
    
    print("A4单张测试:")
    print(f"  成品尺寸: 210 × 297 mm")
    print(f"  计算结果: {result['calculated_results']['required_plate_size']}")
    print(f"  材料利用率: {result['material_efficiency']}")
    
    # 验证计算逻辑
    expected_width = 210 + 2 * 3 + 2 * 5  # 成品 + 出血 + 安全边距
    expected_height = 297 + 2 * 3 + 5 + 12  # 成品 + 出血 + 安全边距 + 咬口
    
    actual_size = result['calculated_results']['required_plate_size']
    size_parts = actual_size.replace(' mm', '').split(' × ')
    actual_width, actual_height = map(float, size_parts[0:2])
    
    if abs(actual_width - expected_width) < 0.1 and abs(actual_height - expected_height) < 0.1:
        print("✓ 计算逻辑正确")
    else:
        print(f"✗ 计算逻辑错误: 期望 {expected_width} × {expected_height}, 实际 {actual_width} × {actual_height}")

if __name__ == "__main__":
    try:
        test_api()
        test_calculation_logic()
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {e}")
        sys.exit(1)
