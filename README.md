# 印版尺寸反向计算推理系统

## 项目概述

本系统是一个专业的印刷行业印版尺寸反向计算工具，能够根据成品尺寸、出血位、安全边距等参数，自动计算所需的印版尺寸，并提供最优拼版方案推荐和成本分析。

## 主要功能

### 🎯 核心计算功能
- **反向推理计算**：从成品尺寸推算印版尺寸
- **多种拼版方案**：支持不同的水平和垂直拼版组合
- **咬口方向选择**：支持长边咬口和短边咬口
- **标准参数设置**：出血位、安全边距、咬口边距等

### 📊 智能分析
- **材料利用率计算**：评估不同方案的材料使用效率
- **印刷机适配性**：自动匹配适用的印刷机规格
- **最优方案推荐**：基于效率和成本的智能推荐
- **成本分析**：提供详细的成本估算

### 🎨 用户界面
- **响应式设计**：适配不同设备屏幕
- **直观操作**：简洁明了的参数输入界面
- **实时计算**：即时显示计算结果
- **可视化展示**：尺寸对比图和数据图表

## 技术架构

### 后端技术
- **框架**：Flask 2.3.3
- **语言**：Python 3.x
- **API设计**：RESTful API

### 前端技术
- **框架**：Bootstrap 5.1.3
- **样式**：CSS3 + 自定义样式
- **交互**：原生JavaScript
- **图标**：Font Awesome 6.0

### 核心算法
- **印版尺寸计算**：基于印刷行业标准的数学模型
- **拼版优化**：多方案对比和效率评估
- **成本分析**：综合考虑材料和工艺成本

## 安装和运行

### 环境要求
- Python 3.7+
- pip 包管理器

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd 印版尺寸计算
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **启动应用**
```bash
python app.py
```

4. **访问系统**
打开浏览器访问：http://127.0.0.1:5000

### 运行测试
```bash
python test_calculator.py
```

## 使用说明

### 基本操作流程

1. **输入成品尺寸**
   - 手动输入宽度和高度（单位：mm）
   - 或从标准尺寸下拉菜单中选择

2. **设置印刷参数**
   - 出血位：通常为3mm
   - 安全边距：推荐5-10mm
   - 拼版数量：水平和垂直方向的拼版数
   - 咬口方向：选择长边或短边咬口

3. **执行计算**
   - 点击"开始计算"按钮
   - 系统自动计算并显示结果

4. **查看结果**
   - 基本计算结果：印版尺寸、材料利用率等
   - 最优方案推荐：不同拼版方案的对比
   - 成本分析：详细的成本估算
   - 可视化展示：尺寸对比图

### 参数说明

| 参数 | 说明 | 推荐值 |
|------|------|--------|
| 成品尺寸 | 最终产品的实际尺寸 | 根据需求设定 |
| 出血位 | 印刷出血区域 | 3mm（标准） |
| 安全边距 | 安全操作区域 | 5-10mm |
| 拼版数量 | 单张印版上的产品数量 | 根据效率优化 |
| 咬口方向 | 印刷机咬纸方向 | 根据印刷机选择 |

## 系统特色

### 🔧 专业性
- 基于印刷行业标准和实践经验
- 考虑实际生产中的各种约束条件
- 提供专业的成本分析和建议

### 🚀 智能化
- 自动推荐最优拼版方案
- 智能匹配适用的印刷机规格
- 实时计算材料利用率

### 💡 易用性
- 直观的用户界面设计
- 丰富的提示和帮助信息
- 支持标准尺寸快速选择

### 📈 可扩展性
- 模块化的代码架构
- 易于添加新的印刷机规格
- 支持自定义计算参数

## 项目结构

```
印版尺寸计算/
├── app.py                 # Flask主应用
├── requirements.txt       # Python依赖
├── test_calculator.py     # 测试脚本
├── README.md             # 项目说明
├── templates/            # HTML模板
│   └── index.html        # 主页面
└── static/              # 静态资源
    ├── style.css        # 样式文件
    └── script.js        # JavaScript脚本
```

## API接口

### 计算接口
- **URL**: `/api/calculate`
- **方法**: POST
- **参数**: 成品尺寸、出血位、安全边距、拼版数量、咬口方向
- **返回**: 计算结果、最优方案、成本分析

### 数据接口
- **纸张尺寸**: `/api/paper-sizes` (GET)
- **印刷机规格**: `/api/press-sizes` (GET)

## 开发说明

### 核心类说明

#### PlateCalculator
主要的计算引擎类，包含以下核心方法：

- `calculate_plate_size()`: 主计算方法
- `_find_suitable_presses()`: 查找适用印刷机
- `_calculate_material_efficiency()`: 计算材料利用率
- `_suggest_optimal_layout()`: 推荐最优方案
- `_calculate_cost_analysis()`: 成本分析

### 扩展开发

1. **添加新的印刷机规格**
   - 在`STANDARD_PRESS_SIZES`中添加新规格

2. **自定义成本参数**
   - 修改`_calculate_cost_analysis()`方法中的成本参数

3. **增加新的计算逻辑**
   - 在`PlateCalculator`类中添加新方法

## 版本历史

- **v1.0.0** (2025-09-12)
  - 初始版本发布
  - 基本计算功能实现
  - Web界面开发完成
  - 测试验证通过

## 许可证

本项目仅供学习和研究使用。

## 项目信息

**项目类型**：印刷行业专业工具
**开发时间**：2025年9月
**技术栈**：Python Flask + HTML/CSS/JavaScript

---

*本系统专为印刷行业设计，旨在提高印版设计效率和材料利用率。*
