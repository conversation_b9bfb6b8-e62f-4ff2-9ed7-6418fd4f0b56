# 印版尺寸计算系统 - 可执行文件使用说明

**版本：v1.0.0**
**文件大小：69.0 MB**
**发布时间：2025年9月**

---

## 📋 系统简介

本系统是一个专业的印刷行业印版尺寸反向计算工具，已打包成独立的可执行文件（.exe），可以在任何Windows电脑上直接运行，**无需安装Python环境**。

## 🎯 主要功能

- ✅ **印版尺寸反向计算**：从成品尺寸推算印版尺寸
- ✅ **智能拼版推荐**：自动推荐最优拼版方案
- ✅ **材料利用率分析**：计算不同方案的材料使用效率
- ✅ **印刷机适配检查**：自动匹配适用的印刷机规格
- ✅ **成本分析**：提供详细的成本估算
- ✅ **可视化展示**：直观的尺寸对比图

## 🚀 快速开始

### 步骤1：获取文件
将 `印版尺寸计算系统.exe` 文件复制到您的电脑上任意位置。

### 步骤2：运行程序
双击 `印版尺寸计算系统.exe` 文件启动程序。

### 步骤3：等待启动
程序启动后会显示以下信息：
```
============================================================
印版尺寸反向计算推理系统
作者：阮西东 (Ruan Xidong)
============================================================

正在启动服务器...
服务器地址: http://127.0.0.1:5000
浏览器将自动打开，如未打开请手动访问上述地址
按 Ctrl+C 停止服务器
============================================================
```

### 步骤4：使用系统
- 浏览器会自动打开并显示系统界面
- 如果浏览器未自动打开，请手动访问：http://127.0.0.1:5000

## 💻 系统要求

### 最低要求
- **操作系统**：Windows 7 或更高版本
- **内存**：至少 2GB RAM
- **硬盘空间**：至少 100MB 可用空间
- **网络**：无需联网（本地运行）

### 推荐配置
- **操作系统**：Windows 10 或 Windows 11
- **内存**：4GB RAM 或更多
- **浏览器**：Chrome、Firefox、Edge 或 Safari

## 📖 使用教程

### 1. 基本操作流程

#### 输入参数
1. **成品尺寸**：输入最终产品的宽度和高度（单位：mm）
2. **出血位**：设置出血区域大小（默认3mm）
3. **安全边距**：设置安全操作区域（默认5mm）
4. **拼版设置**：选择水平和垂直拼版数量
5. **咬口方向**：选择长边咬口或短边咬口

#### 执行计算
点击"开始计算"按钮，系统会立即显示：
- 印版尺寸计算结果
- 材料利用率分析
- 最优方案推荐
- 成本分析
- 可视化尺寸对比图

### 2. 快速选择功能

#### 标准尺寸选择
系统预设了常用的标准纸张尺寸：
- A系列：A0, A1, A2, A3, A4, A5
- B系列：B0, B1, B2, B3, B4, B5

选择标准尺寸后会自动填入对应的宽度和高度。

#### 印刷机规格参考
右侧面板显示了常见印刷机的最大印刷尺寸：
- 对开机：520 × 720 mm
- 四开机：390 × 540 mm
- 八开机：270 × 390 mm
- 全张机：1020 × 720 mm
- 大全张机：1200 × 900 mm

### 3. 结果解读

#### 基本计算结果
- **含出血产品尺寸**：单个产品加上出血位后的尺寸
- **拼版总尺寸**：所有产品拼版后的总尺寸
- **所需印版尺寸**：最终需要的印版尺寸（重点关注）
- **印版面积**：印版的总面积
- **材料利用率**：成品面积占印版面积的百分比

#### 最优方案推荐
系统会自动计算多种拼版方案，并按材料利用率排序：
- **拼版方式**：如"2 × 2"表示水平2个，垂直2个
- **咬口方向**：长边咬口或短边咬口
- **印版尺寸**：该方案所需的印版尺寸
- **材料利用率**：该方案的材料使用效率
- **适用印刷机**：可以使用的印刷机类型

#### 成本分析
- **印版面积**：印版的面积（平方米）
- **印版成本**：印版材料成本
- **开机费**：印刷机开机成本
- **总成本**：印版成本 + 开机费
- **单件成本**：总成本除以产品数量

## ⚠️ 注意事项

### 使用注意
1. **首次启动**：第一次运行可能需要较长时间（30秒-1分钟）
2. **防火墙提示**：Windows可能会询问是否允许程序访问网络，请选择"允许"
3. **端口占用**：如果5000端口被占用，程序可能无法启动
4. **浏览器兼容**：建议使用现代浏览器以获得最佳体验

### 故障排除
1. **程序无法启动**：
   - 检查是否有杀毒软件阻止
   - 尝试以管理员身份运行
   - 确保5000端口未被占用

2. **浏览器未自动打开**：
   - 手动打开浏览器
   - 访问 http://127.0.0.1:5000

3. **计算结果异常**：
   - 检查输入参数是否正确
   - 确保数值为正数
   - 重新输入并计算

## 🔧 技术支持

### 常见问题

**Q: 为什么文件这么大（69MB）？**
A: 文件包含了完整的Python运行环境和所有依赖库，确保在任何Windows电脑上都能运行。

**Q: 是否需要联网？**
A: 不需要。系统完全在本地运行，无需网络连接。

**Q: 可以同时运行多个实例吗？**
A: 不建议。每个实例都会尝试使用5000端口，可能会冲突。

**Q: 如何停止程序？**
A: 在命令行窗口按 Ctrl+C，或直接关闭命令行窗口。

### 技术支持
如有技术问题或建议：
- **项目类型**：印刷行业专业工具
- **开发时间**：2025年9月

## 📝 版本信息

### v1.0.0 (2025-09-12)
- ✅ 初始版本发布
- ✅ 完整的印版尺寸计算功能
- ✅ 智能拼版方案推荐
- ✅ 成本分析功能
- ✅ 可视化界面
- ✅ 独立可执行文件打包

---

**感谢使用印版尺寸计算系统！**

*本系统专为印刷行业设计，旨在提高印版设计效率和材料利用率。*
