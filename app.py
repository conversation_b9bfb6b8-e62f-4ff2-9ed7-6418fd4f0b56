#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
印版尺寸反向计算推理系统
"""

from flask import Flask, render_template, request, jsonify
import math
from typing import Dict, List, Tuple, Optional
import json

app = Flask(__name__)

class PlateCalculator:
    """印版尺寸计算核心引擎"""
    
    # 标准印刷机规格 (mm)
    STANDARD_PRESS_SIZES = {
        "对开机": {"max_width": 520, "max_height": 720},
        "四开机": {"max_width": 390, "max_height": 540},
        "八开机": {"max_width": 270, "max_height": 390},
        "全张机": {"max_width": 1020, "max_height": 720},
        "大全张机": {"max_width": 1200, "max_height": 900}
    }
    
    # 标准纸张尺寸 (mm)
    STANDARD_PAPER_SIZES = {
        "A0": (841, 1189),
        "A1": (594, 841),
        "A2": (420, 594),
        "A3": (297, 420),
        "A4": (210, 297),
        "A5": (148, 210),
        "B0": (1000, 1414),
        "B1": (707, 1000),
        "B2": (500, 707),
        "B3": (353, 500),
        "B4": (250, 353),
        "B5": (176, 250)
    }
    
    def __init__(self):
        self.bleed_standard = 3  # 标准出血位 3mm
        self.safety_margin = 5   # 安全边距 5mm
        self.gripper_margin = 12 # 咬口边距 12mm
        
    def calculate_plate_size(self, 
                           finished_width: float, 
                           finished_height: float,
                           bleed: float = None,
                           safety_margin: float = None,
                           copies_horizontal: int = 1,
                           copies_vertical: int = 1,
                           gripper_edge: str = "long") -> Dict:
        """
        反向计算印版尺寸
        
        Args:
            finished_width: 成品宽度 (mm)
            finished_height: 成品高度 (mm)
            bleed: 出血位 (mm)
            safety_margin: 安全边距 (mm)
            copies_horizontal: 水平拼版数量
            copies_vertical: 垂直拼版数量
            gripper_edge: 咬口方向 ("long"/"short")
            
        Returns:
            计算结果字典
        """
        
        # 使用默认值或传入值
        bleed = bleed or self.bleed_standard
        safety_margin = safety_margin or self.safety_margin
        
        # 单个产品含出血的尺寸
        product_with_bleed_width = finished_width + 2 * bleed
        product_with_bleed_height = finished_height + 2 * bleed
        
        # 拼版后的总尺寸
        total_product_width = product_with_bleed_width * copies_horizontal
        total_product_height = product_with_bleed_height * copies_vertical
        
        # 计算印版尺寸（加上安全边距和咬口）
        if gripper_edge == "long":
            # 长边为咬口
            plate_width = total_product_width + 2 * safety_margin
            plate_height = total_product_height + safety_margin + self.gripper_margin
        else:
            # 短边为咬口
            plate_width = total_product_width + safety_margin + self.gripper_margin
            plate_height = total_product_height + 2 * safety_margin
            
        # 检查适用的印刷机
        suitable_presses = self._find_suitable_presses(plate_width, plate_height)
        
        # 计算材料利用率
        material_efficiency = self._calculate_material_efficiency(
            finished_width, finished_height, plate_width, plate_height, 
            copies_horizontal, copies_vertical
        )
        
        # 推荐最优拼版方案
        optimal_layout = self._suggest_optimal_layout(
            finished_width, finished_height, bleed, safety_margin
        )
        
        return {
            "input_parameters": {
                "finished_size": f"{finished_width} × {finished_height} mm",
                "bleed": f"{bleed} mm",
                "safety_margin": f"{safety_margin} mm",
                "layout": f"{copies_horizontal} × {copies_vertical}",
                "gripper_edge": gripper_edge
            },
            "calculated_results": {
                "product_with_bleed": f"{product_with_bleed_width:.1f} × {product_with_bleed_height:.1f} mm",
                "total_layout_size": f"{total_product_width:.1f} × {total_product_height:.1f} mm",
                "required_plate_size": f"{plate_width:.1f} × {plate_height:.1f} mm",
                "plate_area": f"{(plate_width * plate_height / 1000000):.3f} m²"
            },
            "suitable_presses": suitable_presses,
            "material_efficiency": f"{material_efficiency:.1f}%",
            "optimal_layouts": optimal_layout,
            "cost_analysis": self._calculate_cost_analysis(plate_width, plate_height, copies_horizontal * copies_vertical)
        }
    
    def _find_suitable_presses(self, width: float, height: float) -> List[str]:
        """查找适用的印刷机"""
        suitable = []
        for press_name, specs in self.STANDARD_PRESS_SIZES.items():
            if (width <= specs["max_width"] and height <= specs["max_height"]) or \
               (width <= specs["max_height"] and height <= specs["max_width"]):
                suitable.append(press_name)
        return suitable
    
    def _calculate_material_efficiency(self, fw: float, fh: float, pw: float, ph: float, ch: int, cv: int) -> float:
        """计算材料利用率"""
        finished_area = fw * fh * ch * cv
        plate_area = pw * ph
        return (finished_area / plate_area) * 100 if plate_area > 0 else 0
    
    def _suggest_optimal_layout(self, width: float, height: float, bleed: float, safety: float) -> List[Dict]:
        """推荐最优拼版方案"""
        layouts = []

        # 测试不同的拼版组合（避免递归调用）
        for h_copies in range(1, 5):
            for v_copies in range(1, 5):
                for gripper in ["long", "short"]:
                    # 直接计算而不调用主方法
                    product_with_bleed_width = width + 2 * bleed
                    product_with_bleed_height = height + 2 * bleed

                    total_product_width = product_with_bleed_width * h_copies
                    total_product_height = product_with_bleed_height * v_copies

                    if gripper == "long":
                        plate_width = total_product_width + 2 * safety
                        plate_height = total_product_height + safety + self.gripper_margin
                    else:
                        plate_width = total_product_width + safety + self.gripper_margin
                        plate_height = total_product_height + 2 * safety

                    suitable_presses = self._find_suitable_presses(plate_width, plate_height)
                    efficiency = self._calculate_material_efficiency(
                        width, height, plate_width, plate_height, h_copies, v_copies
                    )

                    # 检查是否有适用的印刷机
                    if suitable_presses:
                        layouts.append({
                            "layout": f"{h_copies} × {v_copies}",
                            "gripper_edge": gripper,
                            "plate_size": f"{plate_width:.1f} × {plate_height:.1f} mm",
                            "efficiency": f"{efficiency:.1f}%",
                            "suitable_presses": suitable_presses
                        })

        # 按效率排序，返回前5个
        layouts.sort(key=lambda x: float(x["efficiency"].rstrip('%')), reverse=True)
        return layouts[:5]
    
    def _calculate_cost_analysis(self, width: float, height: float, copies: int) -> Dict:
        """成本分析"""
        plate_area = (width * height) / 1000000  # 转换为平方米
        
        # 假设成本参数（实际应用中可配置）
        plate_cost_per_sqm = 150  # 印版成本 150元/平方米
        setup_cost = 200  # 开机费 200元
        
        total_plate_cost = plate_area * plate_cost_per_sqm
        total_cost = total_plate_cost + setup_cost
        cost_per_copy = total_cost / copies if copies > 0 else 0
        
        return {
            "plate_area_sqm": f"{plate_area:.3f}",
            "plate_cost": f"{total_plate_cost:.2f}",
            "setup_cost": f"{setup_cost:.2f}",
            "total_cost": f"{total_cost:.2f}",
            "cost_per_copy": f"{cost_per_copy:.2f}"
        }

# 创建计算器实例
calculator = PlateCalculator()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/calculate', methods=['POST'])
def api_calculate():
    """计算API接口"""
    try:
        data = request.get_json()
        
        # 参数验证
        required_params = ['finished_width', 'finished_height']
        for param in required_params:
            if param not in data:
                return jsonify({"error": f"缺少必需参数: {param}"}), 400
        
        # 执行计算
        result = calculator.calculate_plate_size(
            finished_width=float(data['finished_width']),
            finished_height=float(data['finished_height']),
            bleed=float(data.get('bleed', 3)),
            safety_margin=float(data.get('safety_margin', 5)),
            copies_horizontal=int(data.get('copies_horizontal', 1)),
            copies_vertical=int(data.get('copies_vertical', 1)),
            gripper_edge=data.get('gripper_edge', 'long')
        )
        
        return jsonify({"success": True, "data": result})
        
    except ValueError as e:
        return jsonify({"error": f"参数格式错误: {str(e)}"}), 400
    except Exception as e:
        return jsonify({"error": f"计算错误: {str(e)}"}), 500

@app.route('/api/paper-sizes')
def api_paper_sizes():
    """获取标准纸张尺寸"""
    return jsonify(calculator.STANDARD_PAPER_SIZES)

@app.route('/api/press-sizes')
def api_press_sizes():
    """获取印刷机规格"""
    return jsonify(calculator.STANDARD_PRESS_SIZES)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
