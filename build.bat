@echo off
chcp 65001 >nul
echo ========================================
echo 印版尺寸计算系统 - 打包工具
echo ========================================
echo.

echo 正在检查环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境
    pause
    exit /b 1
)

echo 正在清理旧的构建文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del /q "*.spec"

echo.
echo 正在打包应用程序...
echo 这可能需要几分钟时间，请耐心等待...
echo.

pyinstaller --onefile ^
    --name "印版尺寸计算系统" ^
    --add-data "templates;templates" ^
    --add-data "static;static" ^
    --hidden-import "flask" ^
    --hidden-import "werkzeug" ^
    --hidden-import "jinja2" ^
    --hidden-import "markupsafe" ^
    --hidden-import "itsdangerous" ^
    --hidden-import "click" ^
    --hidden-import "blinker" ^
    --exclude-module "tkinter" ^
    --exclude-module "matplotlib" ^
    --exclude-module "numpy" ^
    --exclude-module "pandas" ^
    --console ^
    app_standalone.py

if errorlevel 1 (
    echo.
    echo 打包失败！请检查错误信息。
    pause
    exit /b 1
)

echo.
echo ========================================
echo 打包完成！
echo ========================================
echo.
echo 可执行文件位置: dist\印版尺寸计算系统.exe
echo 文件大小: 
for %%I in ("dist\印版尺寸计算系统.exe") do echo %%~zI 字节

echo.
echo 您可以将 dist\印版尺寸计算系统.exe 复制到任何Windows电脑上运行
echo 无需安装Python环境！
echo.

echo 正在测试可执行文件...
if exist "dist\印版尺寸计算系统.exe" (
    echo ✓ 可执行文件创建成功
    echo.
    echo 是否现在运行测试？(Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo 正在启动测试...
        start "" "dist\印版尺寸计算系统.exe"
    )
) else (
    echo ✗ 可执行文件创建失败
)

echo.
pause
