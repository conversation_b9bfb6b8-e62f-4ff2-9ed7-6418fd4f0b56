#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller打包配置文件
用于将Flask应用打包成独立的可执行文件
"""

import os
import sys
from PyInstaller.utils.hooks import collect_data_files

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 收集模板和静态文件
template_files = collect_data_files('templates')
static_files = collect_data_files('static')

# 手动添加模板和静态文件
datas = [
    ('templates', 'templates'),
    ('static', 'static'),
]

# 隐藏导入的模块
hiddenimports = [
    'flask',
    'werkzeug',
    'jinja2',
    'markupsafe',
    'itsdangerous',
    'click',
    'blinker',
]

# 排除不需要的模块以减小文件大小
excludes = [
    'tkinter',
    'matplotlib',
    'numpy',
    'pandas',
    'scipy',
    'PIL',
    'cv2',
    'tensorflow',
    'torch',
    'sklearn',
]

# PyInstaller配置
a = Analysis(
    ['app.py'],
    pathex=['.'],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='印版尺寸计算系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
