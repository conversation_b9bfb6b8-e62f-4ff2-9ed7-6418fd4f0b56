// 印版尺寸计算系统 JavaScript

class PlateCalculatorApp {
    constructor() {
        this.paperSizes = {};
        this.pressSizes = {};
        this.init();
    }

    async init() {
        await this.loadStandardSizes();
        this.bindEvents();
        this.displayPressSpecs();
    }

    // 加载标准尺寸数据
    async loadStandardSizes() {
        try {
            const [paperResponse, pressResponse] = await Promise.all([
                fetch('/api/paper-sizes'),
                fetch('/api/press-sizes')
            ]);
            
            this.paperSizes = await paperResponse.json();
            this.pressSizes = await pressResponse.json();
            
            this.populateStandardSizes();
        } catch (error) {
            console.error('加载标准尺寸失败:', error);
        }
    }

    // 填充标准尺寸选择框
    populateStandardSizes() {
        const select = document.getElementById('standard_size');
        select.innerHTML = '<option value="">选择标准尺寸...</option>';
        
        for (const [name, [width, height]] of Object.entries(this.paperSizes)) {
            const option = document.createElement('option');
            option.value = `${width},${height}`;
            option.textContent = `${name} (${width} × ${height} mm)`;
            select.appendChild(option);
        }
    }

    // 显示印刷机规格
    displayPressSpecs() {
        const container = document.getElementById('press_specs');
        let html = '';
        
        for (const [name, specs] of Object.entries(this.pressSizes)) {
            html += `
                <div class="press-spec-item">
                    <span class="press-name">${name}</span>
                    <span class="press-size">${specs.max_width} × ${specs.max_height} mm</span>
                </div>
            `;
        }
        
        container.innerHTML = html;
    }

    // 绑定事件
    bindEvents() {
        // 表单提交
        document.getElementById('calculationForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.performCalculation();
        });

        // 标准尺寸选择
        document.getElementById('standard_size').addEventListener('change', (e) => {
            if (e.target.value) {
                const [width, height] = e.target.value.split(',');
                document.getElementById('finished_width').value = width;
                document.getElementById('finished_height').value = height;
            }
        });

        // 实时验证
        ['finished_width', 'finished_height'].forEach(id => {
            document.getElementById(id).addEventListener('input', this.validateInput);
        });
    }

    // 输入验证
    validateInput(e) {
        const value = parseFloat(e.target.value);
        const input = e.target;
        
        if (isNaN(value) || value <= 0) {
            input.classList.add('is-invalid');
        } else {
            input.classList.remove('is-invalid');
        }
    }

    // 执行计算
    async performCalculation() {
        const formData = this.getFormData();
        
        if (!this.validateFormData(formData)) {
            return;
        }

        this.showLoading(true);
        
        try {
            const response = await fetch('/api/calculate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            const result = await response.json();
            
            if (result.success) {
                this.displayResults(result.data);
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('计算请求失败: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    // 获取表单数据
    getFormData() {
        return {
            finished_width: parseFloat(document.getElementById('finished_width').value),
            finished_height: parseFloat(document.getElementById('finished_height').value),
            bleed: parseFloat(document.getElementById('bleed').value),
            safety_margin: parseFloat(document.getElementById('safety_margin').value),
            copies_horizontal: parseInt(document.getElementById('copies_horizontal').value),
            copies_vertical: parseInt(document.getElementById('copies_vertical').value),
            gripper_edge: document.querySelector('input[name="gripper_edge"]:checked').value
        };
    }

    // 验证表单数据
    validateFormData(data) {
        if (!data.finished_width || !data.finished_height) {
            this.showError('请输入成品尺寸');
            return false;
        }
        
        if (data.finished_width <= 0 || data.finished_height <= 0) {
            this.showError('成品尺寸必须大于0');
            return false;
        }
        
        return true;
    }

    // 显示计算结果
    displayResults(data) {
        this.displayBasicResults(data);
        this.displayOptimalLayouts(data.optimal_layouts);
        this.displayCostAnalysis(data.cost_analysis);
        this.displayVisualization(data);
        
        // 显示结果卡片
        ['results_card', 'optimal_card', 'cost_card', 'visualization_card'].forEach(id => {
            document.getElementById(id).style.display = 'block';
            document.getElementById(id).classList.add('fade-in-up');
        });
    }

    // 显示基本计算结果
    displayBasicResults(data) {
        const container = document.getElementById('calculation_results');
        const input = data.input_parameters;
        const results = data.calculated_results;
        
        let html = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary mb-3"><i class="fas fa-info-circle me-2"></i>输入参数</h6>
                    <div class="result-item">
                        <div class="result-label">成品尺寸</div>
                        <div class="result-value">${input.finished_size}</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">出血位</div>
                        <div class="result-value">${input.bleed}</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">安全边距</div>
                        <div class="result-value">${input.safety_margin}</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">拼版方式</div>
                        <div class="result-value">${input.layout}</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">咬口方向</div>
                        <div class="result-value">${input.gripper_edge === 'long' ? '长边咬口' : '短边咬口'}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6 class="text-success mb-3"><i class="fas fa-calculator me-2"></i>计算结果</h6>
                    <div class="result-item">
                        <div class="result-label">含出血产品尺寸</div>
                        <div class="result-value">${results.product_with_bleed}</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">拼版总尺寸</div>
                        <div class="result-value">${results.total_layout_size}</div>
                    </div>
                    <div class="result-item highlight">
                        <div class="result-label">所需印版尺寸</div>
                        <div class="result-value text-primary fw-bold">${results.required_plate_size}</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">印版面积</div>
                        <div class="result-value">${results.plate_area}</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">材料利用率</div>
                        <div class="result-value">${data.material_efficiency}</div>
                    </div>
                </div>
            </div>
        `;
        
        // 适用印刷机
        if (data.suitable_presses.length > 0) {
            html += `
                <div class="mt-3">
                    <h6 class="text-info mb-2"><i class="fas fa-print me-2"></i>适用印刷机</h6>
                    <div class="d-flex flex-wrap gap-2">
                        ${data.suitable_presses.map(press => 
                            `<span class="badge bg-info">${press}</span>`
                        ).join('')}
                    </div>
                </div>
            `;
        } else {
            html += `
                <div class="mt-3">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        警告：当前尺寸超出所有标准印刷机规格，请考虑调整拼版方案
                    </div>
                </div>
            `;
        }
        
        container.innerHTML = html;
    }

    // 显示最优方案
    displayOptimalLayouts(layouts) {
        const container = document.getElementById('optimal_layouts');
        
        if (layouts.length === 0) {
            container.innerHTML = '<p class="text-muted">暂无推荐方案</p>';
            return;
        }
        
        let html = `
            <div class="table-responsive">
                <table class="table table-hover optimal-table">
                    <thead>
                        <tr>
                            <th>拼版方式</th>
                            <th>咬口方向</th>
                            <th>印版尺寸</th>
                            <th>材料利用率</th>
                            <th>适用印刷机</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        layouts.forEach((layout, index) => {
            const efficiency = parseFloat(layout.efficiency.replace('%', ''));
            let badgeClass = 'bg-success';
            if (efficiency < 60) badgeClass = 'bg-danger';
            else if (efficiency < 80) badgeClass = 'bg-warning';
            
            html += `
                <tr ${index === 0 ? 'class="table-success"' : ''}>
                    <td><strong>${layout.layout}</strong></td>
                    <td>${layout.gripper_edge === 'long' ? '长边咬口' : '短边咬口'}</td>
                    <td>${layout.plate_size}</td>
                    <td>
                        <span class="badge efficiency-badge ${badgeClass}">
                            ${layout.efficiency}
                        </span>
                    </td>
                    <td>
                        ${layout.suitable_presses.map(press => 
                            `<small class="badge bg-secondary me-1">${press}</small>`
                        ).join('')}
                    </td>
                </tr>
            `;
        });
        
        html += '</tbody></table></div>';
        
        if (layouts.length > 0) {
            html += `
                <div class="alert alert-info mt-3">
                    <i class="fas fa-lightbulb me-2"></i>
                    <strong>推荐：</strong>第一行方案具有最高的材料利用率，建议优先考虑。
                </div>
            `;
        }
        
        container.innerHTML = html;
    }

    // 显示成本分析
    displayCostAnalysis(cost) {
        const container = document.getElementById('cost_analysis');
        
        const html = `
            <div class="row">
                <div class="col-md-6">
                    <div class="cost-item">
                        <span class="cost-label">印版面积</span>
                        <span class="cost-value">${cost.plate_area_sqm} m²</span>
                    </div>
                    <div class="cost-item">
                        <span class="cost-label">印版成本</span>
                        <span class="cost-value">¥${cost.plate_cost}</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="cost-item">
                        <span class="cost-label">开机费</span>
                        <span class="cost-value">¥${cost.setup_cost}</span>
                    </div>
                    <div class="cost-item">
                        <span class="cost-label">总成本</span>
                        <span class="cost-value text-primary">¥${cost.total_cost}</span>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <div class="cost-item bg-light">
                    <span class="cost-label">单件成本</span>
                    <span class="cost-value text-success fs-5">¥${cost.cost_per_copy}</span>
                </div>
            </div>
            <small class="text-muted mt-2 d-block">
                * 成本仅供参考，实际价格可能因供应商和地区而异
            </small>
        `;
        
        container.innerHTML = html;
    }

    // 显示可视化
    displayVisualization(data) {
        const container = document.getElementById('size_visualization');
        const results = data.calculated_results;
        
        // 解析尺寸
        const plateSize = results.required_plate_size.match(/(\d+\.?\d*)\s*×\s*(\d+\.?\d*)/);
        const productSize = data.input_parameters.finished_size.match(/(\d+\.?\d*)\s*×\s*(\d+\.?\d*)/);
        
        if (!plateSize || !productSize) return;
        
        const plateWidth = parseFloat(plateSize[1]);
        const plateHeight = parseFloat(plateSize[2]);
        const productWidth = parseFloat(productSize[1]);
        const productHeight = parseFloat(productSize[2]);
        
        // 计算缩放比例
        const maxDisplaySize = 300;
        const scale = Math.min(maxDisplaySize / plateWidth, maxDisplaySize / plateHeight);
        
        const displayPlateWidth = plateWidth * scale;
        const displayPlateHeight = plateHeight * scale;
        const displayProductWidth = productWidth * scale;
        const displayProductHeight = productHeight * scale;
        
        const html = `
            <div class="visualization-container">
                <h6 class="mb-3">尺寸对比图</h6>
                <div style="position: relative; display: inline-block;">
                    <div class="size-diagram" 
                         style="width: ${displayPlateWidth}px; height: ${displayPlateHeight}px;">
                        <div class="size-label" style="top: -25px; left: 0;">
                            印版: ${plateWidth} × ${plateHeight} mm
                        </div>
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
                                    width: ${displayProductWidth}px; height: ${displayProductHeight}px;
                                    background: rgba(52, 152, 219, 0.3); border: 2px solid #3498db;">
                            <div class="size-label" style="bottom: -25px; right: 0;">
                                成品: ${productWidth} × ${productHeight} mm
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        外框为印版尺寸，内框为成品尺寸（按比例缩放显示）
                    </small>
                </div>
            </div>
        `;
        
        container.innerHTML = html;
    }

    // 显示/隐藏加载动画
    showLoading(show) {
        document.getElementById('loading_overlay').style.display = show ? 'flex' : 'none';
    }

    // 显示错误信息
    showError(message) {
        document.getElementById('error_message').textContent = message;
        const modal = new bootstrap.Modal(document.getElementById('errorModal'));
        modal.show();
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new PlateCalculatorApp();
});
