/* 印版尺寸计算系统样式表 - 现代化UI设计 */

:root {
    --primary-color: #4f46e5;
    --primary-dark: #3730a3;
    --secondary-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --light-bg: #f8fafc;
    --card-bg: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --border-radius: 12px;
    --border-radius-lg: 16px;
}

body {
    font-family: 'Inter', 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    line-height: 1.6;
    color: var(--text-primary);
    min-height: 100vh;
}

/* 容器样式 */
.container-fluid {
    padding: 0;
}

.container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    margin-top: 2rem;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 头部样式优化 */
header {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-md);
    color: var(--text-primary) !important;
}

header h1 {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 1.8rem;
}

header p {
    color: var(--text-secondary) !important;
}

header small {
    color: var(--text-secondary) !important;
}

/* 卡片样式现代化 */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    background: var(--card-bg);
    box-shadow: var(--shadow-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-4px);
}

.card-header {
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0 !important;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 1.25rem 1.5rem;
}

.card-header h5 {
    font-weight: 600;
    margin: 0;
    font-size: 1.1rem;
}

.card-body {
    padding: 1.5rem;
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
}

/* 结果展示样式 */
.result-item {
    background: #fff;
    border-left: 4px solid var(--secondary-color);
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.result-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.result-value {
    font-size: 1.1em;
    color: #2c3e50;
}

/* 最优方案表格 */
.optimal-table {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.optimal-table th {
    background: var(--secondary-color);
    color: white;
    font-weight: 600;
    border: none;
}

.optimal-table td {
    border-color: #e9ecef;
    vertical-align: middle;
}

.efficiency-badge {
    font-size: 0.9em;
    padding: 5px 10px;
    border-radius: 20px;
}

/* 成本分析样式 */
.cost-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #fff;
    border-radius: 8px;
    margin-bottom: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.cost-label {
    font-weight: 600;
    color: var(--primary-color);
}

.cost-value {
    font-size: 1.1em;
    font-weight: 700;
    color: var(--success-color);
}

/* 可视化样式 */
.visualization-container {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.size-diagram {
    display: inline-block;
    border: 3px solid var(--primary-color);
    background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%), 
                linear-gradient(-45deg, #f8f9fa 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f8f9fa 75%), 
                linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    position: relative;
    margin: 20px;
}

.size-label {
    position: absolute;
    background: rgba(44, 62, 80, 0.9);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    font-weight: 600;
}

/* 印刷机规格样式 */
.press-spec-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #e9ecef;
}

.press-spec-item:last-child {
    border-bottom: none;
}

.press-name {
    font-weight: 600;
    color: var(--primary-color);
}

.press-size {
    color: #6c757d;
    font-size: 0.9em;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: var(--shadow);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 10px;
    }
    
    header h1 {
        font-size: 1.5rem;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .btn-lg {
        padding: 10px 20px;
        font-size: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-success {
    background-color: var(--success-color);
}

.status-warning {
    background-color: var(--warning-color);
}

.status-danger {
    background-color: var(--danger-color);
}

/* 工具提示样式 */
.tooltip-inner {
    background-color: var(--primary-color);
    border-radius: 6px;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: var(--primary-color);
}

/* 高亮效果 */
.highlight {
    background: linear-gradient(120deg, transparent 0%, rgba(52, 152, 219, 0.2) 50%, transparent 100%);
    animation: highlight 2s ease-in-out;
}

@keyframes highlight {
    0%, 100% { background-position: -200% 0; }
    50% { background-position: 200% 0; }
}

/* 打印样式 */
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    body {
        background: white;
    }
}
