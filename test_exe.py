#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试打包后的可执行文件
"""

import requests
import time
import subprocess
import threading
import sys
import os

def test_exe():
    """测试可执行文件"""
    exe_path = os.path.join("dist", "印版尺寸计算系统.exe")
    
    if not os.path.exists(exe_path):
        print("❌ 可执行文件不存在")
        return False
    
    print("🚀 启动可执行文件测试...")
    print(f"📁 文件路径: {exe_path}")
    print(f"📊 文件大小: {os.path.getsize(exe_path) / (1024*1024):.1f} MB")
    
    # 启动可执行文件
    try:
        process = subprocess.Popen([exe_path], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True)
        
        print("⏳ 等待服务器启动...")
        time.sleep(8)  # 等待服务器启动
        
        # 测试API
        print("🔍 测试API接口...")
        
        # 测试基本连接
        try:
            response = requests.get("http://127.0.0.1:5000/api/paper-sizes", timeout=5)
            if response.status_code == 200:
                print("✅ API连接成功")
                
                # 测试计算功能
                test_data = {
                    "finished_width": 210,
                    "finished_height": 297,
                    "bleed": 3,
                    "safety_margin": 5,
                    "copies_horizontal": 1,
                    "copies_vertical": 1,
                    "gripper_edge": "long"
                }
                
                calc_response = requests.post("http://127.0.0.1:5000/api/calculate", 
                                            json=test_data, timeout=10)
                
                if calc_response.status_code == 200:
                    result = calc_response.json()
                    if result.get('success'):
                        print("✅ 计算功能正常")
                        data = result['data']
                        print(f"   📏 计算结果: {data['calculated_results']['required_plate_size']}")
                        print(f"   📈 材料利用率: {data['material_efficiency']}")
                        print("✅ 可执行文件测试通过！")
                        return True
                    else:
                        print("❌ 计算功能异常")
                        return False
                else:
                    print(f"❌ 计算API错误: {calc_response.status_code}")
                    return False
            else:
                print(f"❌ API连接失败: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 启动可执行文件失败: {e}")
        return False
    
    finally:
        # 清理进程
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            try:
                process.kill()
            except:
                pass

if __name__ == "__main__":
    print("=" * 50)
    print("印版尺寸计算系统 - 可执行文件测试")
    print("=" * 50)
    
    success = test_exe()
    
    if success:
        print("\n🎉 测试完成！可执行文件工作正常。")
        print("\n📋 使用说明:")
        print("1. 将 dist/印版尺寸计算系统.exe 复制到目标电脑")
        print("2. 双击运行，等待浏览器自动打开")
        print("3. 如浏览器未自动打开，请手动访问 http://127.0.0.1:5000")
        print("4. 按 Ctrl+C 停止程序")
    else:
        print("\n❌ 测试失败！请检查打包过程。")
    
    print("=" * 50)
