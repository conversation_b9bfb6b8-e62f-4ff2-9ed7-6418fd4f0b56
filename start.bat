@echo off
chcp 65001 >nul
echo ========================================
echo 印版尺寸反向计算推理系统
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境，请先安装Python 3.7+
    pause
    exit /b 1
)

echo 正在安装依赖包...
pip install -r requirements.txt

echo.
echo 正在启动应用程序...
echo 请在浏览器中访问: http://127.0.0.1:5000
echo 按 Ctrl+C 停止服务器
echo.

python app.py

pause
