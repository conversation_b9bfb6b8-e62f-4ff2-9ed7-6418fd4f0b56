('C:\\Users\\<USER>\\Desktop\\中钞设计制版实习\\印版尺寸计算\\build\\印版尺寸计算系统\\PYZ-00.pyz',
 [('IPython',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\__init__.py',
   'PYMODULE'),
  ('IPython.core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\__init__.py',
   'PYMODULE'),
  ('IPython.core.alias',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\alias.py',
   'PYMODULE'),
  ('IPython.core.application',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\application.py',
   'PYMODULE'),
  ('IPython.core.async_helpers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\async_helpers.py',
   'PYMODULE'),
  ('IPython.core.autocall',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\autocall.py',
   'PYMODULE'),
  ('IPython.core.builtin_trap',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\builtin_trap.py',
   'PYMODULE'),
  ('IPython.core.compilerop',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\compilerop.py',
   'PYMODULE'),
  ('IPython.core.completer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\completer.py',
   'PYMODULE'),
  ('IPython.core.completerlib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\completerlib.py',
   'PYMODULE'),
  ('IPython.core.crashhandler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\crashhandler.py',
   'PYMODULE'),
  ('IPython.core.debugger',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\debugger.py',
   'PYMODULE'),
  ('IPython.core.display',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\display.py',
   'PYMODULE'),
  ('IPython.core.display_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\display_functions.py',
   'PYMODULE'),
  ('IPython.core.display_trap',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\display_trap.py',
   'PYMODULE'),
  ('IPython.core.displayhook',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\displayhook.py',
   'PYMODULE'),
  ('IPython.core.displaypub',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\displaypub.py',
   'PYMODULE'),
  ('IPython.core.error',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\error.py',
   'PYMODULE'),
  ('IPython.core.events',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\events.py',
   'PYMODULE'),
  ('IPython.core.excolors',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\excolors.py',
   'PYMODULE'),
  ('IPython.core.extensions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\extensions.py',
   'PYMODULE'),
  ('IPython.core.formatters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\formatters.py',
   'PYMODULE'),
  ('IPython.core.getipython',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\getipython.py',
   'PYMODULE'),
  ('IPython.core.guarded_eval',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\guarded_eval.py',
   'PYMODULE'),
  ('IPython.core.history',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\history.py',
   'PYMODULE'),
  ('IPython.core.hooks',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\hooks.py',
   'PYMODULE'),
  ('IPython.core.inputtransformer2',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\inputtransformer2.py',
   'PYMODULE'),
  ('IPython.core.interactiveshell',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\interactiveshell.py',
   'PYMODULE'),
  ('IPython.core.latex_symbols',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\latex_symbols.py',
   'PYMODULE'),
  ('IPython.core.logger',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\logger.py',
   'PYMODULE'),
  ('IPython.core.macro',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\macro.py',
   'PYMODULE'),
  ('IPython.core.magic',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magic.py',
   'PYMODULE'),
  ('IPython.core.magic_arguments',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magic_arguments.py',
   'PYMODULE'),
  ('IPython.core.magics',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magics\\__init__.py',
   'PYMODULE'),
  ('IPython.core.magics.ast_mod',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magics\\ast_mod.py',
   'PYMODULE'),
  ('IPython.core.magics.auto',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magics\\auto.py',
   'PYMODULE'),
  ('IPython.core.magics.basic',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magics\\basic.py',
   'PYMODULE'),
  ('IPython.core.magics.code',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magics\\code.py',
   'PYMODULE'),
  ('IPython.core.magics.config',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magics\\config.py',
   'PYMODULE'),
  ('IPython.core.magics.display',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magics\\display.py',
   'PYMODULE'),
  ('IPython.core.magics.execution',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magics\\execution.py',
   'PYMODULE'),
  ('IPython.core.magics.extension',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magics\\extension.py',
   'PYMODULE'),
  ('IPython.core.magics.history',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magics\\history.py',
   'PYMODULE'),
  ('IPython.core.magics.logging',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magics\\logging.py',
   'PYMODULE'),
  ('IPython.core.magics.namespace',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magics\\namespace.py',
   'PYMODULE'),
  ('IPython.core.magics.osm',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magics\\osm.py',
   'PYMODULE'),
  ('IPython.core.magics.packaging',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magics\\packaging.py',
   'PYMODULE'),
  ('IPython.core.magics.pylab',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magics\\pylab.py',
   'PYMODULE'),
  ('IPython.core.magics.script',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\magics\\script.py',
   'PYMODULE'),
  ('IPython.core.oinspect',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\oinspect.py',
   'PYMODULE'),
  ('IPython.core.page',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\page.py',
   'PYMODULE'),
  ('IPython.core.payload',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\payload.py',
   'PYMODULE'),
  ('IPython.core.payloadpage',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\payloadpage.py',
   'PYMODULE'),
  ('IPython.core.prefilter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\prefilter.py',
   'PYMODULE'),
  ('IPython.core.profiledir',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\profiledir.py',
   'PYMODULE'),
  ('IPython.core.pylabtools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\pylabtools.py',
   'PYMODULE'),
  ('IPython.core.release',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\release.py',
   'PYMODULE'),
  ('IPython.core.shellapp',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\shellapp.py',
   'PYMODULE'),
  ('IPython.core.splitinput',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\splitinput.py',
   'PYMODULE'),
  ('IPython.core.ultratb',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\ultratb.py',
   'PYMODULE'),
  ('IPython.core.usage',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\core\\usage.py',
   'PYMODULE'),
  ('IPython.display',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\display.py',
   'PYMODULE'),
  ('IPython.extensions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\extensions\\__init__.py',
   'PYMODULE'),
  ('IPython.extensions.storemagic',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\extensions\\storemagic.py',
   'PYMODULE'),
  ('IPython.external',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\external\\__init__.py',
   'PYMODULE'),
  ('IPython.external.qt_for_kernel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\external\\qt_for_kernel.py',
   'PYMODULE'),
  ('IPython.external.qt_loaders',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\external\\qt_loaders.py',
   'PYMODULE'),
  ('IPython.lib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\lib\\__init__.py',
   'PYMODULE'),
  ('IPython.lib.clipboard',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\lib\\clipboard.py',
   'PYMODULE'),
  ('IPython.lib.display',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\lib\\display.py',
   'PYMODULE'),
  ('IPython.lib.guisupport',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\lib\\guisupport.py',
   'PYMODULE'),
  ('IPython.lib.pretty',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\lib\\pretty.py',
   'PYMODULE'),
  ('IPython.paths',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\paths.py',
   'PYMODULE'),
  ('IPython.terminal',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\terminal\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.debugger',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\terminal\\debugger.py',
   'PYMODULE'),
  ('IPython.terminal.embed',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\terminal\\embed.py',
   'PYMODULE'),
  ('IPython.terminal.interactiveshell',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\terminal\\interactiveshell.py',
   'PYMODULE'),
  ('IPython.terminal.ipapp',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\terminal\\ipapp.py',
   'PYMODULE'),
  ('IPython.terminal.magics',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\terminal\\magics.py',
   'PYMODULE'),
  ('IPython.terminal.prompts',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\terminal\\prompts.py',
   'PYMODULE'),
  ('IPython.terminal.pt_inputhooks',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\terminal\\pt_inputhooks\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.ptutils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\terminal\\ptutils.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.auto_match',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\auto_match.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.auto_suggest',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\auto_suggest.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.filters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\filters.py',
   'PYMODULE'),
  ('IPython.testing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\testing\\__init__.py',
   'PYMODULE'),
  ('IPython.testing.skipdoctest',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\testing\\skipdoctest.py',
   'PYMODULE'),
  ('IPython.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\__init__.py',
   'PYMODULE'),
  ('IPython.utils.PyColorize',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\PyColorize.py',
   'PYMODULE'),
  ('IPython.utils._process_cli',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\_process_cli.py',
   'PYMODULE'),
  ('IPython.utils._process_common',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\_process_common.py',
   'PYMODULE'),
  ('IPython.utils._process_emscripten',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\_process_emscripten.py',
   'PYMODULE'),
  ('IPython.utils._process_posix',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\_process_posix.py',
   'PYMODULE'),
  ('IPython.utils._process_win32',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\_process_win32.py',
   'PYMODULE'),
  ('IPython.utils._sysinfo',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\_sysinfo.py',
   'PYMODULE'),
  ('IPython.utils.capture',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\capture.py',
   'PYMODULE'),
  ('IPython.utils.colorable',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\colorable.py',
   'PYMODULE'),
  ('IPython.utils.coloransi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\coloransi.py',
   'PYMODULE'),
  ('IPython.utils.contexts',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\contexts.py',
   'PYMODULE'),
  ('IPython.utils.data',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\data.py',
   'PYMODULE'),
  ('IPython.utils.decorators',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\decorators.py',
   'PYMODULE'),
  ('IPython.utils.dir2',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\dir2.py',
   'PYMODULE'),
  ('IPython.utils.docs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\docs.py',
   'PYMODULE'),
  ('IPython.utils.encoding',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\encoding.py',
   'PYMODULE'),
  ('IPython.utils.frame',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\frame.py',
   'PYMODULE'),
  ('IPython.utils.generics',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\generics.py',
   'PYMODULE'),
  ('IPython.utils.importstring',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\importstring.py',
   'PYMODULE'),
  ('IPython.utils.io',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\io.py',
   'PYMODULE'),
  ('IPython.utils.ipstruct',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\ipstruct.py',
   'PYMODULE'),
  ('IPython.utils.module_paths',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\module_paths.py',
   'PYMODULE'),
  ('IPython.utils.openpy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\openpy.py',
   'PYMODULE'),
  ('IPython.utils.path',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\path.py',
   'PYMODULE'),
  ('IPython.utils.process',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\process.py',
   'PYMODULE'),
  ('IPython.utils.py3compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\py3compat.py',
   'PYMODULE'),
  ('IPython.utils.sentinel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\sentinel.py',
   'PYMODULE'),
  ('IPython.utils.strdispatch',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\strdispatch.py',
   'PYMODULE'),
  ('IPython.utils.sysinfo',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\sysinfo.py',
   'PYMODULE'),
  ('IPython.utils.syspathcontext',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\syspathcontext.py',
   'PYMODULE'),
  ('IPython.utils.terminal',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\terminal.py',
   'PYMODULE'),
  ('IPython.utils.text',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\text.py',
   'PYMODULE'),
  ('IPython.utils.timing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\timing.py',
   'PYMODULE'),
  ('IPython.utils.tokenutil',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\tokenutil.py',
   'PYMODULE'),
  ('IPython.utils.wildcard',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\IPython\\utils\\wildcard.py',
   'PYMODULE'),
  ('PIL',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt5',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__', 'C:\\Users\\<USER>\\anaconda3\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Users\\<USER>\\anaconda3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\Users\\<USER>\\anaconda3\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Users\\<USER>\\anaconda3\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'C:\\Users\\<USER>\\anaconda3\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'C:\\Users\\<USER>\\anaconda3\\Lib\\argparse.py', 'PYMODULE'),
  ('arrow',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\arrow\\__init__.py',
   'PYMODULE'),
  ('arrow._version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\arrow\\_version.py',
   'PYMODULE'),
  ('arrow.api',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\arrow\\api.py',
   'PYMODULE'),
  ('arrow.arrow',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\arrow\\arrow.py',
   'PYMODULE'),
  ('arrow.constants',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\arrow\\constants.py',
   'PYMODULE'),
  ('arrow.factory',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\arrow\\factory.py',
   'PYMODULE'),
  ('arrow.formatter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\arrow\\formatter.py',
   'PYMODULE'),
  ('arrow.locales',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\arrow\\locales.py',
   'PYMODULE'),
  ('arrow.parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\arrow\\parser.py',
   'PYMODULE'),
  ('arrow.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\arrow\\util.py',
   'PYMODULE'),
  ('ast', 'C:\\Users\\<USER>\\anaconda3\\Lib\\ast.py', 'PYMODULE'),
  ('asttokens',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\asttokens\\__init__.py',
   'PYMODULE'),
  ('asttokens.asttokens',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\asttokens\\asttokens.py',
   'PYMODULE'),
  ('asttokens.line_numbers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\asttokens\\line_numbers.py',
   'PYMODULE'),
  ('asttokens.mark_tokens',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\asttokens\\mark_tokens.py',
   'PYMODULE'),
  ('asttokens.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\asttokens\\util.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attrs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attrs\\__init__.py',
   'PYMODULE'),
  ('attrs.converters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attrs\\converters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attrs\\exceptions.py',
   'PYMODULE'),
  ('attrs.filters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attrs\\filters.py',
   'PYMODULE'),
  ('attrs.setters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attrs\\setters.py',
   'PYMODULE'),
  ('attrs.validators',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\attrs\\validators.py',
   'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'C:\\Users\\<USER>\\anaconda3\\Lib\\base64.py', 'PYMODULE'),
  ('bcrypt', '-', 'PYMODULE'),
  ('bdb', 'C:\\Users\\<USER>\\anaconda3\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'C:\\Users\\<USER>\\anaconda3\\Lib\\bisect.py', 'PYMODULE'),
  ('blinker',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('brotli',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\brotli.py',
   'PYMODULE'),
  ('bz2', 'C:\\Users\\<USER>\\anaconda3\\Lib\\bz2.py', 'PYMODULE'),
  ('cProfile', 'C:\\Users\\<USER>\\anaconda3\\Lib\\cProfile.py', 'PYMODULE'),
  ('calendar', 'C:\\Users\\<USER>\\anaconda3\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('chardet',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('click',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cloudpickle',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cloudpickle\\__init__.py',
   'PYMODULE'),
  ('cloudpickle.cloudpickle',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cloudpickle\\cloudpickle.py',
   'PYMODULE'),
  ('cloudpickle.cloudpickle_fast',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cloudpickle\\cloudpickle_fast.py',
   'PYMODULE'),
  ('cloudpickle.compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cloudpickle\\compat.py',
   'PYMODULE'),
  ('cmd', 'C:\\Users\\<USER>\\anaconda3\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\Users\\<USER>\\anaconda3\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Users\\<USER>\\anaconda3\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'C:\\Users\\<USER>\\anaconda3\\Lib\\colorsys.py', 'PYMODULE'),
  ('comm',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comm\\__init__.py',
   'PYMODULE'),
  ('comm.base_comm',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comm\\base_comm.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'C:\\Users\\<USER>\\anaconda3\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'C:\\Users\\<USER>\\anaconda3\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'C:\\Users\\<USER>\\anaconda3\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses', 'C:\\Users\\<USER>\\anaconda3\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\curses\\has_key.py',
   'PYMODULE'),
  ('dataclasses', 'C:\\Users\\<USER>\\anaconda3\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'C:\\Users\\<USER>\\anaconda3\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('debugpy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\debugpy\\__init__.py',
   'PYMODULE'),
  ('debugpy._vendored',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\debugpy\\_vendored\\__init__.py',
   'PYMODULE'),
  ('debugpy._vendored._util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\debugpy\\_vendored\\_util.py',
   'PYMODULE'),
  ('debugpy._vendored.force_pydevd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\debugpy\\_vendored\\force_pydevd.py',
   'PYMODULE'),
  ('debugpy._version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\debugpy\\_version.py',
   'PYMODULE'),
  ('debugpy.adapter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\debugpy\\adapter\\__init__.py',
   'PYMODULE'),
  ('debugpy.common',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\debugpy\\common\\__init__.py',
   'PYMODULE'),
  ('debugpy.common.json',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\debugpy\\common\\json.py',
   'PYMODULE'),
  ('debugpy.common.log',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\debugpy\\common\\log.py',
   'PYMODULE'),
  ('debugpy.common.sockets',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\debugpy\\common\\sockets.py',
   'PYMODULE'),
  ('debugpy.common.timestamp',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\debugpy\\common\\timestamp.py',
   'PYMODULE'),
  ('debugpy.common.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\debugpy\\common\\util.py',
   'PYMODULE'),
  ('debugpy.public_api',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\debugpy\\public_api.py',
   'PYMODULE'),
  ('debugpy.server',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\debugpy\\server\\__init__.py',
   'PYMODULE'),
  ('debugpy.server.api',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\debugpy\\server\\api.py',
   'PYMODULE'),
  ('decimal', 'C:\\Users\\<USER>\\anaconda3\\Lib\\decimal.py', 'PYMODULE'),
  ('decorator',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\decorator.py',
   'PYMODULE'),
  ('defusedxml',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('difflib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\Users\\<USER>\\anaconda3\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'C:\\Users\\<USER>\\anaconda3\\Lib\\doctest.py', 'PYMODULE'),
  ('dotenv',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email', 'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('executing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\executing\\__init__.py',
   'PYMODULE'),
  ('executing.executing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\executing\\executing.py',
   'PYMODULE'),
  ('executing.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\executing\\version.py',
   'PYMODULE'),
  ('fastjsonschema',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\fastjsonschema\\__init__.py',
   'PYMODULE'),
  ('fastjsonschema.draft04',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\fastjsonschema\\draft04.py',
   'PYMODULE'),
  ('fastjsonschema.draft06',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\fastjsonschema\\draft06.py',
   'PYMODULE'),
  ('fastjsonschema.draft07',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\fastjsonschema\\draft07.py',
   'PYMODULE'),
  ('fastjsonschema.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\fastjsonschema\\exceptions.py',
   'PYMODULE'),
  ('fastjsonschema.generator',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\fastjsonschema\\generator.py',
   'PYMODULE'),
  ('fastjsonschema.indent',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\fastjsonschema\\indent.py',
   'PYMODULE'),
  ('fastjsonschema.ref_resolver',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\fastjsonschema\\ref_resolver.py',
   'PYMODULE'),
  ('fastjsonschema.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\fastjsonschema\\version.py',
   'PYMODULE'),
  ('filecmp', 'C:\\Users\\<USER>\\anaconda3\\Lib\\filecmp.py', 'PYMODULE'),
  ('flask',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.app',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.scaffold',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.wrappers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Users\\<USER>\\anaconda3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fqdn',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\fqdn\\__init__.py',
   'PYMODULE'),
  ('fqdn._compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\fqdn\\_compat.py',
   'PYMODULE'),
  ('fractions', 'C:\\Users\\<USER>\\anaconda3\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Users\\<USER>\\anaconda3\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Users\\<USER>\\anaconda3\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Users\\<USER>\\anaconda3\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Users\\<USER>\\anaconda3\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\Users\\<USER>\\anaconda3\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Users\\<USER>\\anaconda3\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Users\\<USER>\\anaconda3\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'C:\\Users\\<USER>\\anaconda3\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_metadata',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata._typing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\_typing.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('inspect', 'C:\\Users\\<USER>\\anaconda3\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Users\\<USER>\\anaconda3\\Lib\\ipaddress.py', 'PYMODULE'),
  ('ipykernel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\__init__.py',
   'PYMODULE'),
  ('ipykernel._eventloop_macos',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\_eventloop_macos.py',
   'PYMODULE'),
  ('ipykernel._version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\_version.py',
   'PYMODULE'),
  ('ipykernel.comm',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\comm\\__init__.py',
   'PYMODULE'),
  ('ipykernel.comm.comm',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\comm\\comm.py',
   'PYMODULE'),
  ('ipykernel.comm.manager',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\comm\\manager.py',
   'PYMODULE'),
  ('ipykernel.compiler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\compiler.py',
   'PYMODULE'),
  ('ipykernel.connect',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\connect.py',
   'PYMODULE'),
  ('ipykernel.control',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\control.py',
   'PYMODULE'),
  ('ipykernel.debugger',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\debugger.py',
   'PYMODULE'),
  ('ipykernel.displayhook',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\displayhook.py',
   'PYMODULE'),
  ('ipykernel.embed',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\embed.py',
   'PYMODULE'),
  ('ipykernel.eventloops',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\eventloops.py',
   'PYMODULE'),
  ('ipykernel.gui',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\gui\\__init__.py',
   'PYMODULE'),
  ('ipykernel.gui.gtk3embed',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\gui\\gtk3embed.py',
   'PYMODULE'),
  ('ipykernel.gui.gtkembed',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\gui\\gtkembed.py',
   'PYMODULE'),
  ('ipykernel.heartbeat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\heartbeat.py',
   'PYMODULE'),
  ('ipykernel.iostream',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\iostream.py',
   'PYMODULE'),
  ('ipykernel.ipkernel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\ipkernel.py',
   'PYMODULE'),
  ('ipykernel.jsonutil',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\jsonutil.py',
   'PYMODULE'),
  ('ipykernel.kernelapp',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\kernelapp.py',
   'PYMODULE'),
  ('ipykernel.kernelbase',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\kernelbase.py',
   'PYMODULE'),
  ('ipykernel.kernelspec',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\kernelspec.py',
   'PYMODULE'),
  ('ipykernel.parentpoller',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\parentpoller.py',
   'PYMODULE'),
  ('ipykernel.pickleutil',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\pickleutil.py',
   'PYMODULE'),
  ('ipykernel.serialize',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\serialize.py',
   'PYMODULE'),
  ('ipykernel.trio_runner',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\trio_runner.py',
   'PYMODULE'),
  ('ipykernel.zmqshell',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\ipykernel\\zmqshell.py',
   'PYMODULE'),
  ('isoduration',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\isoduration\\__init__.py',
   'PYMODULE'),
  ('isoduration.constants',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\isoduration\\constants.py',
   'PYMODULE'),
  ('isoduration.formatter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\isoduration\\formatter\\__init__.py',
   'PYMODULE'),
  ('isoduration.formatter.checking',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\isoduration\\formatter\\checking.py',
   'PYMODULE'),
  ('isoduration.formatter.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\isoduration\\formatter\\exceptions.py',
   'PYMODULE'),
  ('isoduration.formatter.formatting',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\isoduration\\formatter\\formatting.py',
   'PYMODULE'),
  ('isoduration.operations',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\isoduration\\operations\\__init__.py',
   'PYMODULE'),
  ('isoduration.operations.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\isoduration\\operations\\util.py',
   'PYMODULE'),
  ('isoduration.parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\isoduration\\parser\\__init__.py',
   'PYMODULE'),
  ('isoduration.parser.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\isoduration\\parser\\exceptions.py',
   'PYMODULE'),
  ('isoduration.parser.parsing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\isoduration\\parser\\parsing.py',
   'PYMODULE'),
  ('isoduration.parser.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\isoduration\\parser\\util.py',
   'PYMODULE'),
  ('isoduration.types',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\isoduration\\types.py',
   'PYMODULE'),
  ('itsdangerous',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jedi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\__init__.py',
   'PYMODULE'),
  ('jedi._compatibility',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\_compatibility.py',
   'PYMODULE'),
  ('jedi.api',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\api\\__init__.py',
   'PYMODULE'),
  ('jedi.api.classes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\api\\classes.py',
   'PYMODULE'),
  ('jedi.api.completion',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\api\\completion.py',
   'PYMODULE'),
  ('jedi.api.completion_cache',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\api\\completion_cache.py',
   'PYMODULE'),
  ('jedi.api.environment',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\api\\environment.py',
   'PYMODULE'),
  ('jedi.api.errors',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\api\\errors.py',
   'PYMODULE'),
  ('jedi.api.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\api\\exceptions.py',
   'PYMODULE'),
  ('jedi.api.file_name',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\api\\file_name.py',
   'PYMODULE'),
  ('jedi.api.helpers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\api\\helpers.py',
   'PYMODULE'),
  ('jedi.api.interpreter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\api\\interpreter.py',
   'PYMODULE'),
  ('jedi.api.keywords',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\api\\keywords.py',
   'PYMODULE'),
  ('jedi.api.project',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\api\\project.py',
   'PYMODULE'),
  ('jedi.api.refactoring',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\api\\refactoring\\__init__.py',
   'PYMODULE'),
  ('jedi.api.refactoring.extract',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\api\\refactoring\\extract.py',
   'PYMODULE'),
  ('jedi.api.strings',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\api\\strings.py',
   'PYMODULE'),
  ('jedi.cache',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\cache.py',
   'PYMODULE'),
  ('jedi.common',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\common.py',
   'PYMODULE'),
  ('jedi.debug',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\debug.py',
   'PYMODULE'),
  ('jedi.file_io',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\file_io.py',
   'PYMODULE'),
  ('jedi.inference',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.analysis',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\analysis.py',
   'PYMODULE'),
  ('jedi.inference.arguments',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\arguments.py',
   'PYMODULE'),
  ('jedi.inference.base_value',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\base_value.py',
   'PYMODULE'),
  ('jedi.inference.cache',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\cache.py',
   'PYMODULE'),
  ('jedi.inference.compiled',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\compiled\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.compiled.access',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\compiled\\access.py',
   'PYMODULE'),
  ('jedi.inference.compiled.getattr_static',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\compiled\\getattr_static.py',
   'PYMODULE'),
  ('jedi.inference.compiled.mixed',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\compiled\\mixed.py',
   'PYMODULE'),
  ('jedi.inference.compiled.subprocess',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\compiled\\subprocess\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.compiled.subprocess.functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\compiled\\subprocess\\functions.py',
   'PYMODULE'),
  ('jedi.inference.compiled.value',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\compiled\\value.py',
   'PYMODULE'),
  ('jedi.inference.context',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\context.py',
   'PYMODULE'),
  ('jedi.inference.docstring_utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\docstring_utils.py',
   'PYMODULE'),
  ('jedi.inference.docstrings',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\docstrings.py',
   'PYMODULE'),
  ('jedi.inference.dynamic_params',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\dynamic_params.py',
   'PYMODULE'),
  ('jedi.inference.filters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\filters.py',
   'PYMODULE'),
  ('jedi.inference.finder',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\finder.py',
   'PYMODULE'),
  ('jedi.inference.flow_analysis',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\flow_analysis.py',
   'PYMODULE'),
  ('jedi.inference.gradual',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\gradual\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.gradual.annotation',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\gradual\\annotation.py',
   'PYMODULE'),
  ('jedi.inference.gradual.base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\gradual\\base.py',
   'PYMODULE'),
  ('jedi.inference.gradual.conversion',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\gradual\\conversion.py',
   'PYMODULE'),
  ('jedi.inference.gradual.generics',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\gradual\\generics.py',
   'PYMODULE'),
  ('jedi.inference.gradual.stub_value',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\gradual\\stub_value.py',
   'PYMODULE'),
  ('jedi.inference.gradual.type_var',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\gradual\\type_var.py',
   'PYMODULE'),
  ('jedi.inference.gradual.typeshed',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\gradual\\typeshed.py',
   'PYMODULE'),
  ('jedi.inference.gradual.typing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\gradual\\typing.py',
   'PYMODULE'),
  ('jedi.inference.gradual.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\gradual\\utils.py',
   'PYMODULE'),
  ('jedi.inference.helpers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\helpers.py',
   'PYMODULE'),
  ('jedi.inference.imports',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\imports.py',
   'PYMODULE'),
  ('jedi.inference.lazy_value',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\lazy_value.py',
   'PYMODULE'),
  ('jedi.inference.names',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\names.py',
   'PYMODULE'),
  ('jedi.inference.param',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\param.py',
   'PYMODULE'),
  ('jedi.inference.parser_cache',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\parser_cache.py',
   'PYMODULE'),
  ('jedi.inference.recursion',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\recursion.py',
   'PYMODULE'),
  ('jedi.inference.references',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\references.py',
   'PYMODULE'),
  ('jedi.inference.signature',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\signature.py',
   'PYMODULE'),
  ('jedi.inference.star_args',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\star_args.py',
   'PYMODULE'),
  ('jedi.inference.syntax_tree',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\syntax_tree.py',
   'PYMODULE'),
  ('jedi.inference.sys_path',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\sys_path.py',
   'PYMODULE'),
  ('jedi.inference.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\utils.py',
   'PYMODULE'),
  ('jedi.inference.value',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\value\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.value.decorator',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\value\\decorator.py',
   'PYMODULE'),
  ('jedi.inference.value.dynamic_arrays',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\value\\dynamic_arrays.py',
   'PYMODULE'),
  ('jedi.inference.value.function',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\value\\function.py',
   'PYMODULE'),
  ('jedi.inference.value.instance',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\value\\instance.py',
   'PYMODULE'),
  ('jedi.inference.value.iterable',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\value\\iterable.py',
   'PYMODULE'),
  ('jedi.inference.value.klass',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\value\\klass.py',
   'PYMODULE'),
  ('jedi.inference.value.module',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\value\\module.py',
   'PYMODULE'),
  ('jedi.inference.value.namespace',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\inference\\value\\namespace.py',
   'PYMODULE'),
  ('jedi.parser_utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\parser_utils.py',
   'PYMODULE'),
  ('jedi.plugins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\plugins\\__init__.py',
   'PYMODULE'),
  ('jedi.plugins.django',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\plugins\\django.py',
   'PYMODULE'),
  ('jedi.plugins.flask',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\plugins\\flask.py',
   'PYMODULE'),
  ('jedi.plugins.pytest',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\plugins\\pytest.py',
   'PYMODULE'),
  ('jedi.plugins.registry',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\plugins\\registry.py',
   'PYMODULE'),
  ('jedi.plugins.stdlib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\plugins\\stdlib.py',
   'PYMODULE'),
  ('jedi.settings',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jedi\\settings.py',
   'PYMODULE'),
  ('jinja2',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'C:\\Users\\<USER>\\anaconda3\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('jsonpointer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jsonpointer.py',
   'PYMODULE'),
  ('jsonschema',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jsonschema\\__init__.py',
   'PYMODULE'),
  ('jsonschema._format',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jsonschema\\_format.py',
   'PYMODULE'),
  ('jsonschema._keywords',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jsonschema\\_keywords.py',
   'PYMODULE'),
  ('jsonschema._legacy_keywords',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jsonschema\\_legacy_keywords.py',
   'PYMODULE'),
  ('jsonschema._types',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jsonschema\\_types.py',
   'PYMODULE'),
  ('jsonschema._typing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jsonschema\\_typing.py',
   'PYMODULE'),
  ('jsonschema._utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jsonschema\\_utils.py',
   'PYMODULE'),
  ('jsonschema.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jsonschema\\exceptions.py',
   'PYMODULE'),
  ('jsonschema.protocols',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jsonschema\\protocols.py',
   'PYMODULE'),
  ('jsonschema.validators',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jsonschema\\validators.py',
   'PYMODULE'),
  ('jsonschema_specifications',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jsonschema_specifications\\__init__.py',
   'PYMODULE'),
  ('jsonschema_specifications._core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jsonschema_specifications\\_core.py',
   'PYMODULE'),
  ('jupyter_client',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\__init__.py',
   'PYMODULE'),
  ('jupyter_client._version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\_version.py',
   'PYMODULE'),
  ('jupyter_client.adapter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\adapter.py',
   'PYMODULE'),
  ('jupyter_client.asynchronous',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\asynchronous\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.asynchronous.client',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\asynchronous\\client.py',
   'PYMODULE'),
  ('jupyter_client.blocking',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\blocking\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.blocking.client',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\blocking\\client.py',
   'PYMODULE'),
  ('jupyter_client.channels',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\channels.py',
   'PYMODULE'),
  ('jupyter_client.channelsabc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\channelsabc.py',
   'PYMODULE'),
  ('jupyter_client.client',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\client.py',
   'PYMODULE'),
  ('jupyter_client.clientabc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\clientabc.py',
   'PYMODULE'),
  ('jupyter_client.connect',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\connect.py',
   'PYMODULE'),
  ('jupyter_client.jsonutil',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\jsonutil.py',
   'PYMODULE'),
  ('jupyter_client.kernelspec',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\kernelspec.py',
   'PYMODULE'),
  ('jupyter_client.launcher',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\launcher.py',
   'PYMODULE'),
  ('jupyter_client.localinterfaces',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\localinterfaces.py',
   'PYMODULE'),
  ('jupyter_client.manager',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\manager.py',
   'PYMODULE'),
  ('jupyter_client.managerabc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\managerabc.py',
   'PYMODULE'),
  ('jupyter_client.multikernelmanager',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\multikernelmanager.py',
   'PYMODULE'),
  ('jupyter_client.provisioning',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\provisioning\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.factory',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\provisioning\\factory.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.local_provisioner',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\provisioning\\local_provisioner.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.provisioner_base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\provisioning\\provisioner_base.py',
   'PYMODULE'),
  ('jupyter_client.session',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\session.py',
   'PYMODULE'),
  ('jupyter_client.ssh',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\ssh\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.ssh.forward',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\ssh\\forward.py',
   'PYMODULE'),
  ('jupyter_client.ssh.tunnel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\ssh\\tunnel.py',
   'PYMODULE'),
  ('jupyter_client.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\utils.py',
   'PYMODULE'),
  ('jupyter_client.win_interrupt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_client\\win_interrupt.py',
   'PYMODULE'),
  ('jupyter_core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_core\\__init__.py',
   'PYMODULE'),
  ('jupyter_core.paths',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_core\\paths.py',
   'PYMODULE'),
  ('jupyter_core.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_core\\utils\\__init__.py',
   'PYMODULE'),
  ('jupyter_core.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\jupyter_core\\version.py',
   'PYMODULE'),
  ('lib2to3',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\lib2to3\\__init__.py',
   'PYMODULE'),
  ('lib2to3.pgen2',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\lib2to3\\pgen2\\__init__.py',
   'PYMODULE'),
  ('lib2to3.pgen2.token',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\lib2to3\\pgen2\\token.py',
   'PYMODULE'),
  ('lib2to3.pgen2.tokenize',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\lib2to3\\pgen2\\tokenize.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.config',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\logging\\config.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma', 'C:\\Users\\<USER>\\anaconda3\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('matplotlib_inline',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\matplotlib_inline\\__init__.py',
   'PYMODULE'),
  ('matplotlib_inline.backend_inline',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\matplotlib_inline\\backend_inline.py',
   'PYMODULE'),
  ('matplotlib_inline.config',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\matplotlib_inline\\config.py',
   'PYMODULE'),
  ('mimetypes', 'C:\\Users\\<USER>\\anaconda3\\Lib\\mimetypes.py', 'PYMODULE'),
  ('more_itertools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('more_itertools.more',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\more_itertools\\more.py',
   'PYMODULE'),
  ('more_itertools.recipes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('nbformat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\__init__.py',
   'PYMODULE'),
  ('nbformat._imports',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\_imports.py',
   'PYMODULE'),
  ('nbformat._struct',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\_struct.py',
   'PYMODULE'),
  ('nbformat._version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\_version.py',
   'PYMODULE'),
  ('nbformat.converter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\converter.py',
   'PYMODULE'),
  ('nbformat.corpus',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\corpus\\__init__.py',
   'PYMODULE'),
  ('nbformat.corpus.words',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\corpus\\words.py',
   'PYMODULE'),
  ('nbformat.json_compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\json_compat.py',
   'PYMODULE'),
  ('nbformat.notebooknode',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\notebooknode.py',
   'PYMODULE'),
  ('nbformat.reader',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\reader.py',
   'PYMODULE'),
  ('nbformat.sentinel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\sentinel.py',
   'PYMODULE'),
  ('nbformat.v1',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v1\\__init__.py',
   'PYMODULE'),
  ('nbformat.v1.convert',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v1\\convert.py',
   'PYMODULE'),
  ('nbformat.v1.nbbase',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v1\\nbbase.py',
   'PYMODULE'),
  ('nbformat.v1.nbjson',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v1\\nbjson.py',
   'PYMODULE'),
  ('nbformat.v1.rwbase',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v1\\rwbase.py',
   'PYMODULE'),
  ('nbformat.v2',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v2\\__init__.py',
   'PYMODULE'),
  ('nbformat.v2.convert',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v2\\convert.py',
   'PYMODULE'),
  ('nbformat.v2.nbbase',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v2\\nbbase.py',
   'PYMODULE'),
  ('nbformat.v2.nbjson',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v2\\nbjson.py',
   'PYMODULE'),
  ('nbformat.v2.nbpy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v2\\nbpy.py',
   'PYMODULE'),
  ('nbformat.v2.nbxml',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v2\\nbxml.py',
   'PYMODULE'),
  ('nbformat.v2.rwbase',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v2\\rwbase.py',
   'PYMODULE'),
  ('nbformat.v3',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v3\\__init__.py',
   'PYMODULE'),
  ('nbformat.v3.convert',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v3\\convert.py',
   'PYMODULE'),
  ('nbformat.v3.nbbase',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v3\\nbbase.py',
   'PYMODULE'),
  ('nbformat.v3.nbjson',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v3\\nbjson.py',
   'PYMODULE'),
  ('nbformat.v3.nbpy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v3\\nbpy.py',
   'PYMODULE'),
  ('nbformat.v3.rwbase',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v3\\rwbase.py',
   'PYMODULE'),
  ('nbformat.v4',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v4\\__init__.py',
   'PYMODULE'),
  ('nbformat.v4.convert',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v4\\convert.py',
   'PYMODULE'),
  ('nbformat.v4.nbbase',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v4\\nbbase.py',
   'PYMODULE'),
  ('nbformat.v4.nbjson',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v4\\nbjson.py',
   'PYMODULE'),
  ('nbformat.v4.rwbase',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\v4\\rwbase.py',
   'PYMODULE'),
  ('nbformat.validator',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\validator.py',
   'PYMODULE'),
  ('nbformat.warnings',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nbformat\\warnings.py',
   'PYMODULE'),
  ('nest_asyncio',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\nest_asyncio.py',
   'PYMODULE'),
  ('netrc', 'C:\\Users\\<USER>\\anaconda3\\Lib\\netrc.py', 'PYMODULE'),
  ('ntsecuritycon',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\win32\\lib\\ntsecuritycon.py',
   'PYMODULE'),
  ('nturl2path', 'C:\\Users\\<USER>\\anaconda3\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'C:\\Users\\<USER>\\anaconda3\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.array_api',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'C:\\Users\\<USER>\\anaconda3\\Lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'C:\\Users\\<USER>\\anaconda3\\Lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('parso',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\__init__.py',
   'PYMODULE'),
  ('parso._compatibility',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\_compatibility.py',
   'PYMODULE'),
  ('parso.cache',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\cache.py',
   'PYMODULE'),
  ('parso.file_io',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\file_io.py',
   'PYMODULE'),
  ('parso.grammar',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\grammar.py',
   'PYMODULE'),
  ('parso.normalizer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\normalizer.py',
   'PYMODULE'),
  ('parso.parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\parser.py',
   'PYMODULE'),
  ('parso.pgen2',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\pgen2\\__init__.py',
   'PYMODULE'),
  ('parso.pgen2.generator',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\pgen2\\generator.py',
   'PYMODULE'),
  ('parso.pgen2.grammar_parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\pgen2\\grammar_parser.py',
   'PYMODULE'),
  ('parso.python',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\python\\__init__.py',
   'PYMODULE'),
  ('parso.python.diff',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\python\\diff.py',
   'PYMODULE'),
  ('parso.python.errors',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\python\\errors.py',
   'PYMODULE'),
  ('parso.python.parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\python\\parser.py',
   'PYMODULE'),
  ('parso.python.pep8',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\python\\pep8.py',
   'PYMODULE'),
  ('parso.python.prefix',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\python\\prefix.py',
   'PYMODULE'),
  ('parso.python.token',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\python\\token.py',
   'PYMODULE'),
  ('parso.python.tokenize',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\python\\tokenize.py',
   'PYMODULE'),
  ('parso.python.tree',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\python\\tree.py',
   'PYMODULE'),
  ('parso.tree',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\tree.py',
   'PYMODULE'),
  ('parso.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\parso\\utils.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'C:\\Users\\<USER>\\anaconda3\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'C:\\Users\\<USER>\\anaconda3\\Lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'C:\\Users\\<USER>\\anaconda3\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Users\\<USER>\\anaconda3\\Lib\\platform.py', 'PYMODULE'),
  ('platformdirs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.api',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('plistlib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'C:\\Users\\<USER>\\anaconda3\\Lib\\pprint.py', 'PYMODULE'),
  ('profile', 'C:\\Users\\<USER>\\anaconda3\\Lib\\profile.py', 'PYMODULE'),
  ('prompt_toolkit',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.application',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\application\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.application.application',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\application\\application.py',
   'PYMODULE'),
  ('prompt_toolkit.application.current',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\application\\current.py',
   'PYMODULE'),
  ('prompt_toolkit.application.dummy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\application\\dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.application.run_in_terminal',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\application\\run_in_terminal.py',
   'PYMODULE'),
  ('prompt_toolkit.auto_suggest',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.buffer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\buffer.py',
   'PYMODULE'),
  ('prompt_toolkit.cache',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\cache.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\clipboard\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\clipboard\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.in_memory',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\clipboard\\in_memory.py',
   'PYMODULE'),
  ('prompt_toolkit.completion',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\completion\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\completion\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.deduplicate',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\completion\\deduplicate.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.filesystem',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\completion\\filesystem.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.fuzzy_completer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\completion\\fuzzy_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.nested',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\completion\\nested.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.word_completer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\completion\\word_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.cursor_shapes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\cursor_shapes.py',
   'PYMODULE'),
  ('prompt_toolkit.data_structures',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\data_structures.py',
   'PYMODULE'),
  ('prompt_toolkit.document',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\document.py',
   'PYMODULE'),
  ('prompt_toolkit.enums',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\enums.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\eventloop\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.async_generator',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\eventloop\\async_generator.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.inputhook',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\eventloop\\inputhook.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\eventloop\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.win32',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\eventloop\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.filters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\filters\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.app',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\filters\\app.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\filters\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.cli',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\filters\\cli.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\filters\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.ansi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\ansi.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.html',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\html.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.pygments',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.history',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\history.py',
   'PYMODULE'),
  ('prompt_toolkit.input',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\input\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.input.ansi_escape_sequences',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\input\\ansi_escape_sequences.py',
   'PYMODULE'),
  ('prompt_toolkit.input.base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\input\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.input.defaults',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\input\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_pipe',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\input\\posix_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\input\\posix_utils.py',
   'PYMODULE'),
  ('prompt_toolkit.input.typeahead',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\input\\typeahead.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\input\\vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100_parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\input\\vt100_parser.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\input\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32_pipe',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\input\\win32_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.auto_suggest',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.basic',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\basic.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.completion',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\completion.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.cpr',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\cpr.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.emacs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\emacs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.focus',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\focus.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.mouse',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\mouse.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.named_commands',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\named_commands.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.open_in_editor',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\open_in_editor.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.page_navigation',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\page_navigation.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.scroll',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\scroll.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.search',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\search.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.vi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\vi.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.defaults',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.digraphs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\digraphs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.emacs_state',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\emacs_state.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_bindings',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\key_bindings.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_processor',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\key_processor.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.vi_state',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\key_binding\\vi_state.py',
   'PYMODULE'),
  ('prompt_toolkit.keys',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\keys.py',
   'PYMODULE'),
  ('prompt_toolkit.layout',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\layout\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.containers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\layout\\containers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.controls',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\layout\\controls.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dimension',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\layout\\dimension.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dummy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\layout\\dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.layout',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\layout\\layout.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.margins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\layout\\margins.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.menus',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\layout\\menus.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.mouse_handlers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\layout\\mouse_handlers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.processors',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\layout\\processors.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.screen',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\layout\\screen.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.scrollable_pane',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\layout\\scrollable_pane.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\layout\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\lexers\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\lexers\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.pygments',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\lexers\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.mouse_events',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\mouse_events.py',
   'PYMODULE'),
  ('prompt_toolkit.output',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\output\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.output.base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\output\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.output.color_depth',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\output\\color_depth.py',
   'PYMODULE'),
  ('prompt_toolkit.output.conemu',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\output\\conemu.py',
   'PYMODULE'),
  ('prompt_toolkit.output.defaults',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\output\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.output.flush_stdout',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\output\\flush_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.output.plain_text',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\output\\plain_text.py',
   'PYMODULE'),
  ('prompt_toolkit.output.vt100',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\output\\vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.output.win32',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\output\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.output.windows10',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\output\\windows10.py',
   'PYMODULE'),
  ('prompt_toolkit.patch_stdout',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\patch_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.renderer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\renderer.py',
   'PYMODULE'),
  ('prompt_toolkit.search',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\search.py',
   'PYMODULE'),
  ('prompt_toolkit.selection',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\selection.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.dialogs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.formatters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\formatters.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.prompt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\prompt.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.styles',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\styles\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\styles\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.defaults',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\styles\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.named_colors',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\styles\\named_colors.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.pygments',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\styles\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\styles\\style.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style_transformation',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\styles\\style_transformation.py',
   'PYMODULE'),
  ('prompt_toolkit.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.validation',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\validation.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\widgets\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\widgets\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.dialogs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\widgets\\dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.menus',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\widgets\\menus.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.toolbars',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\widgets\\toolbars.py',
   'PYMODULE'),
  ('prompt_toolkit.win32_types',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\prompt_toolkit\\win32_types.py',
   'PYMODULE'),
  ('pstats', 'C:\\Users\\<USER>\\anaconda3\\Lib\\pstats.py', 'PYMODULE'),
  ('psutil',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('pure_eval',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pure_eval\\__init__.py',
   'PYMODULE'),
  ('pure_eval.core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pure_eval\\core.py',
   'PYMODULE'),
  ('pure_eval.my_getattr_static',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pure_eval\\my_getattr_static.py',
   'PYMODULE'),
  ('pure_eval.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pure_eval\\utils.py',
   'PYMODULE'),
  ('pure_eval.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pure_eval\\version.py',
   'PYMODULE'),
  ('py_compile', 'C:\\Users\\<USER>\\anaconda3\\Lib\\py_compile.py', 'PYMODULE'),
  ('pycparser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'C:\\Users\\<USER>\\anaconda3\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pygments',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\__init__.py',
   'PYMODULE'),
  ('pygments.console',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\console.py',
   'PYMODULE'),
  ('pygments.filter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\filter.py',
   'PYMODULE'),
  ('pygments.filters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\filters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\formatter.py',
   'PYMODULE'),
  ('pygments.formatters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\formatters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatters._mapping',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\formatters\\_mapping.py',
   'PYMODULE'),
  ('pygments.formatters.bbcode',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\formatters\\bbcode.py',
   'PYMODULE'),
  ('pygments.formatters.groff',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\formatters\\groff.py',
   'PYMODULE'),
  ('pygments.formatters.html',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\formatters\\html.py',
   'PYMODULE'),
  ('pygments.formatters.img',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\formatters\\img.py',
   'PYMODULE'),
  ('pygments.formatters.irc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\formatters\\irc.py',
   'PYMODULE'),
  ('pygments.formatters.latex',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\formatters\\latex.py',
   'PYMODULE'),
  ('pygments.formatters.other',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\formatters\\other.py',
   'PYMODULE'),
  ('pygments.formatters.pangomarkup',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\formatters\\pangomarkup.py',
   'PYMODULE'),
  ('pygments.formatters.rtf',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\formatters\\rtf.py',
   'PYMODULE'),
  ('pygments.formatters.svg',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\formatters\\svg.py',
   'PYMODULE'),
  ('pygments.formatters.terminal',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\formatters\\terminal.py',
   'PYMODULE'),
  ('pygments.formatters.terminal256',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\formatters\\terminal256.py',
   'PYMODULE'),
  ('pygments.lexer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexer.py',
   'PYMODULE'),
  ('pygments.lexers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\__init__.py',
   'PYMODULE'),
  ('pygments.lexers._ada_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_ada_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._asy_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_asy_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cl_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_cl_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cocoa_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_cocoa_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._csound_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_csound_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._css_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_css_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._julia_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_julia_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lasso_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_lasso_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lilypond_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_lilypond_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lua_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_lua_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mapping',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_mapping.py',
   'PYMODULE'),
  ('pygments.lexers._mql_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_mql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mysql_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_mysql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._openedge_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_openedge_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._php_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_php_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._postgres_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_postgres_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._qlik_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_qlik_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scheme_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_scheme_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scilab_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_scilab_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sourcemod_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stan_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_stan_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stata_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_stata_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._tsql_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_tsql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._usd_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_usd_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vbscript_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_vbscript_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vim_builtins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\_vim_builtins.py',
   'PYMODULE'),
  ('pygments.lexers.actionscript',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\actionscript.py',
   'PYMODULE'),
  ('pygments.lexers.ada',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\ada.py',
   'PYMODULE'),
  ('pygments.lexers.agile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\agile.py',
   'PYMODULE'),
  ('pygments.lexers.algebra',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\algebra.py',
   'PYMODULE'),
  ('pygments.lexers.ambient',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\ambient.py',
   'PYMODULE'),
  ('pygments.lexers.amdgpu',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\amdgpu.py',
   'PYMODULE'),
  ('pygments.lexers.ampl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\ampl.py',
   'PYMODULE'),
  ('pygments.lexers.apdlexer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\apdlexer.py',
   'PYMODULE'),
  ('pygments.lexers.apl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\apl.py',
   'PYMODULE'),
  ('pygments.lexers.archetype',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\archetype.py',
   'PYMODULE'),
  ('pygments.lexers.arrow',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\arrow.py',
   'PYMODULE'),
  ('pygments.lexers.arturo',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\arturo.py',
   'PYMODULE'),
  ('pygments.lexers.asc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\asc.py',
   'PYMODULE'),
  ('pygments.lexers.asm',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\asm.py',
   'PYMODULE'),
  ('pygments.lexers.automation',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\automation.py',
   'PYMODULE'),
  ('pygments.lexers.bare',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\bare.py',
   'PYMODULE'),
  ('pygments.lexers.basic',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\basic.py',
   'PYMODULE'),
  ('pygments.lexers.bdd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\bdd.py',
   'PYMODULE'),
  ('pygments.lexers.berry',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\berry.py',
   'PYMODULE'),
  ('pygments.lexers.bibtex',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\bibtex.py',
   'PYMODULE'),
  ('pygments.lexers.boa',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\boa.py',
   'PYMODULE'),
  ('pygments.lexers.business',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\business.py',
   'PYMODULE'),
  ('pygments.lexers.c_cpp',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\c_cpp.py',
   'PYMODULE'),
  ('pygments.lexers.c_like',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\c_like.py',
   'PYMODULE'),
  ('pygments.lexers.capnproto',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\capnproto.py',
   'PYMODULE'),
  ('pygments.lexers.carbon',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\carbon.py',
   'PYMODULE'),
  ('pygments.lexers.cddl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\cddl.py',
   'PYMODULE'),
  ('pygments.lexers.chapel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\chapel.py',
   'PYMODULE'),
  ('pygments.lexers.clean',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\clean.py',
   'PYMODULE'),
  ('pygments.lexers.comal',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\comal.py',
   'PYMODULE'),
  ('pygments.lexers.compiled',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\compiled.py',
   'PYMODULE'),
  ('pygments.lexers.configs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\configs.py',
   'PYMODULE'),
  ('pygments.lexers.console',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\console.py',
   'PYMODULE'),
  ('pygments.lexers.cplint',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\cplint.py',
   'PYMODULE'),
  ('pygments.lexers.crystal',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\crystal.py',
   'PYMODULE'),
  ('pygments.lexers.csound',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\csound.py',
   'PYMODULE'),
  ('pygments.lexers.css',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\css.py',
   'PYMODULE'),
  ('pygments.lexers.d',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\d.py',
   'PYMODULE'),
  ('pygments.lexers.dalvik',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\dalvik.py',
   'PYMODULE'),
  ('pygments.lexers.data',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\data.py',
   'PYMODULE'),
  ('pygments.lexers.dax',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\dax.py',
   'PYMODULE'),
  ('pygments.lexers.devicetree',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\devicetree.py',
   'PYMODULE'),
  ('pygments.lexers.diff',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\diff.py',
   'PYMODULE'),
  ('pygments.lexers.dotnet',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\dotnet.py',
   'PYMODULE'),
  ('pygments.lexers.dsls',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\dsls.py',
   'PYMODULE'),
  ('pygments.lexers.dylan',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\dylan.py',
   'PYMODULE'),
  ('pygments.lexers.ecl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\ecl.py',
   'PYMODULE'),
  ('pygments.lexers.eiffel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\eiffel.py',
   'PYMODULE'),
  ('pygments.lexers.elm',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\elm.py',
   'PYMODULE'),
  ('pygments.lexers.elpi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\elpi.py',
   'PYMODULE'),
  ('pygments.lexers.email',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\email.py',
   'PYMODULE'),
  ('pygments.lexers.erlang',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\erlang.py',
   'PYMODULE'),
  ('pygments.lexers.esoteric',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\esoteric.py',
   'PYMODULE'),
  ('pygments.lexers.ezhil',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\ezhil.py',
   'PYMODULE'),
  ('pygments.lexers.factor',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\factor.py',
   'PYMODULE'),
  ('pygments.lexers.fantom',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\fantom.py',
   'PYMODULE'),
  ('pygments.lexers.felix',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\felix.py',
   'PYMODULE'),
  ('pygments.lexers.fift',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\fift.py',
   'PYMODULE'),
  ('pygments.lexers.floscript',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\floscript.py',
   'PYMODULE'),
  ('pygments.lexers.forth',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\forth.py',
   'PYMODULE'),
  ('pygments.lexers.fortran',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\fortran.py',
   'PYMODULE'),
  ('pygments.lexers.foxpro',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\foxpro.py',
   'PYMODULE'),
  ('pygments.lexers.freefem',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\freefem.py',
   'PYMODULE'),
  ('pygments.lexers.func',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\func.py',
   'PYMODULE'),
  ('pygments.lexers.functional',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\functional.py',
   'PYMODULE'),
  ('pygments.lexers.futhark',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\futhark.py',
   'PYMODULE'),
  ('pygments.lexers.gcodelexer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\gcodelexer.py',
   'PYMODULE'),
  ('pygments.lexers.gdscript',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\gdscript.py',
   'PYMODULE'),
  ('pygments.lexers.go',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\go.py',
   'PYMODULE'),
  ('pygments.lexers.grammar_notation',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\grammar_notation.py',
   'PYMODULE'),
  ('pygments.lexers.graph',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\graph.py',
   'PYMODULE'),
  ('pygments.lexers.graphics',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\graphics.py',
   'PYMODULE'),
  ('pygments.lexers.graphviz',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\graphviz.py',
   'PYMODULE'),
  ('pygments.lexers.gsql',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\gsql.py',
   'PYMODULE'),
  ('pygments.lexers.haskell',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\haskell.py',
   'PYMODULE'),
  ('pygments.lexers.haxe',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\haxe.py',
   'PYMODULE'),
  ('pygments.lexers.hdl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\hdl.py',
   'PYMODULE'),
  ('pygments.lexers.hexdump',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\hexdump.py',
   'PYMODULE'),
  ('pygments.lexers.html',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\html.py',
   'PYMODULE'),
  ('pygments.lexers.idl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\idl.py',
   'PYMODULE'),
  ('pygments.lexers.igor',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\igor.py',
   'PYMODULE'),
  ('pygments.lexers.inferno',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\inferno.py',
   'PYMODULE'),
  ('pygments.lexers.installers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\installers.py',
   'PYMODULE'),
  ('pygments.lexers.int_fiction',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\int_fiction.py',
   'PYMODULE'),
  ('pygments.lexers.iolang',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\iolang.py',
   'PYMODULE'),
  ('pygments.lexers.j',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\j.py',
   'PYMODULE'),
  ('pygments.lexers.javascript',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\javascript.py',
   'PYMODULE'),
  ('pygments.lexers.jmespath',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\jmespath.py',
   'PYMODULE'),
  ('pygments.lexers.jslt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\jslt.py',
   'PYMODULE'),
  ('pygments.lexers.jsonnet',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\jsonnet.py',
   'PYMODULE'),
  ('pygments.lexers.julia',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\julia.py',
   'PYMODULE'),
  ('pygments.lexers.jvm',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\jvm.py',
   'PYMODULE'),
  ('pygments.lexers.kuin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\kuin.py',
   'PYMODULE'),
  ('pygments.lexers.lilypond',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\lilypond.py',
   'PYMODULE'),
  ('pygments.lexers.lisp',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\lisp.py',
   'PYMODULE'),
  ('pygments.lexers.macaulay2',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\macaulay2.py',
   'PYMODULE'),
  ('pygments.lexers.make',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\make.py',
   'PYMODULE'),
  ('pygments.lexers.markup',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\markup.py',
   'PYMODULE'),
  ('pygments.lexers.math',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\math.py',
   'PYMODULE'),
  ('pygments.lexers.matlab',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\matlab.py',
   'PYMODULE'),
  ('pygments.lexers.maxima',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\maxima.py',
   'PYMODULE'),
  ('pygments.lexers.meson',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\meson.py',
   'PYMODULE'),
  ('pygments.lexers.mime',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\mime.py',
   'PYMODULE'),
  ('pygments.lexers.minecraft',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\minecraft.py',
   'PYMODULE'),
  ('pygments.lexers.mips',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\mips.py',
   'PYMODULE'),
  ('pygments.lexers.ml',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\ml.py',
   'PYMODULE'),
  ('pygments.lexers.modeling',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\modeling.py',
   'PYMODULE'),
  ('pygments.lexers.modula2',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\modula2.py',
   'PYMODULE'),
  ('pygments.lexers.monte',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\monte.py',
   'PYMODULE'),
  ('pygments.lexers.mosel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\mosel.py',
   'PYMODULE'),
  ('pygments.lexers.ncl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\ncl.py',
   'PYMODULE'),
  ('pygments.lexers.nimrod',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\nimrod.py',
   'PYMODULE'),
  ('pygments.lexers.nit',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\nit.py',
   'PYMODULE'),
  ('pygments.lexers.nix',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\nix.py',
   'PYMODULE'),
  ('pygments.lexers.oberon',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\oberon.py',
   'PYMODULE'),
  ('pygments.lexers.objective',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\objective.py',
   'PYMODULE'),
  ('pygments.lexers.ooc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\ooc.py',
   'PYMODULE'),
  ('pygments.lexers.other',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\other.py',
   'PYMODULE'),
  ('pygments.lexers.parasail',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\parasail.py',
   'PYMODULE'),
  ('pygments.lexers.parsers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\parsers.py',
   'PYMODULE'),
  ('pygments.lexers.pascal',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\pascal.py',
   'PYMODULE'),
  ('pygments.lexers.pawn',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\pawn.py',
   'PYMODULE'),
  ('pygments.lexers.perl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\perl.py',
   'PYMODULE'),
  ('pygments.lexers.phix',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\phix.py',
   'PYMODULE'),
  ('pygments.lexers.php',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\php.py',
   'PYMODULE'),
  ('pygments.lexers.pointless',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\pointless.py',
   'PYMODULE'),
  ('pygments.lexers.pony',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\pony.py',
   'PYMODULE'),
  ('pygments.lexers.praat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\praat.py',
   'PYMODULE'),
  ('pygments.lexers.procfile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\procfile.py',
   'PYMODULE'),
  ('pygments.lexers.prolog',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\prolog.py',
   'PYMODULE'),
  ('pygments.lexers.promql',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\promql.py',
   'PYMODULE'),
  ('pygments.lexers.python',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\python.py',
   'PYMODULE'),
  ('pygments.lexers.q',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\q.py',
   'PYMODULE'),
  ('pygments.lexers.qlik',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\qlik.py',
   'PYMODULE'),
  ('pygments.lexers.qvt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\qvt.py',
   'PYMODULE'),
  ('pygments.lexers.r',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\r.py',
   'PYMODULE'),
  ('pygments.lexers.rdf',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\rdf.py',
   'PYMODULE'),
  ('pygments.lexers.rebol',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\rebol.py',
   'PYMODULE'),
  ('pygments.lexers.resource',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\resource.py',
   'PYMODULE'),
  ('pygments.lexers.ride',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\ride.py',
   'PYMODULE'),
  ('pygments.lexers.rita',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\rita.py',
   'PYMODULE'),
  ('pygments.lexers.rnc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\rnc.py',
   'PYMODULE'),
  ('pygments.lexers.roboconf',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\roboconf.py',
   'PYMODULE'),
  ('pygments.lexers.robotframework',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\robotframework.py',
   'PYMODULE'),
  ('pygments.lexers.ruby',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\ruby.py',
   'PYMODULE'),
  ('pygments.lexers.rust',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\rust.py',
   'PYMODULE'),
  ('pygments.lexers.sas',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\sas.py',
   'PYMODULE'),
  ('pygments.lexers.savi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\savi.py',
   'PYMODULE'),
  ('pygments.lexers.scdoc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\scdoc.py',
   'PYMODULE'),
  ('pygments.lexers.scripting',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\scripting.py',
   'PYMODULE'),
  ('pygments.lexers.sgf',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\sgf.py',
   'PYMODULE'),
  ('pygments.lexers.shell',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\shell.py',
   'PYMODULE'),
  ('pygments.lexers.sieve',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\sieve.py',
   'PYMODULE'),
  ('pygments.lexers.slash',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\slash.py',
   'PYMODULE'),
  ('pygments.lexers.smalltalk',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\smalltalk.py',
   'PYMODULE'),
  ('pygments.lexers.smithy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\smithy.py',
   'PYMODULE'),
  ('pygments.lexers.smv',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\smv.py',
   'PYMODULE'),
  ('pygments.lexers.snobol',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\snobol.py',
   'PYMODULE'),
  ('pygments.lexers.solidity',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\solidity.py',
   'PYMODULE'),
  ('pygments.lexers.sophia',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\sophia.py',
   'PYMODULE'),
  ('pygments.lexers.special',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\special.py',
   'PYMODULE'),
  ('pygments.lexers.spice',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\spice.py',
   'PYMODULE'),
  ('pygments.lexers.sql',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\sql.py',
   'PYMODULE'),
  ('pygments.lexers.srcinfo',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\srcinfo.py',
   'PYMODULE'),
  ('pygments.lexers.stata',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\stata.py',
   'PYMODULE'),
  ('pygments.lexers.supercollider',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\supercollider.py',
   'PYMODULE'),
  ('pygments.lexers.tal',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\tal.py',
   'PYMODULE'),
  ('pygments.lexers.tcl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\tcl.py',
   'PYMODULE'),
  ('pygments.lexers.teal',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\teal.py',
   'PYMODULE'),
  ('pygments.lexers.templates',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\templates.py',
   'PYMODULE'),
  ('pygments.lexers.teraterm',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\teraterm.py',
   'PYMODULE'),
  ('pygments.lexers.testing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\testing.py',
   'PYMODULE'),
  ('pygments.lexers.text',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\text.py',
   'PYMODULE'),
  ('pygments.lexers.textedit',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\textedit.py',
   'PYMODULE'),
  ('pygments.lexers.textfmts',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\textfmts.py',
   'PYMODULE'),
  ('pygments.lexers.theorem',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\theorem.py',
   'PYMODULE'),
  ('pygments.lexers.thingsdb',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\thingsdb.py',
   'PYMODULE'),
  ('pygments.lexers.tlb',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\tlb.py',
   'PYMODULE'),
  ('pygments.lexers.tnt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\tnt.py',
   'PYMODULE'),
  ('pygments.lexers.trafficscript',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\trafficscript.py',
   'PYMODULE'),
  ('pygments.lexers.typoscript',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\typoscript.py',
   'PYMODULE'),
  ('pygments.lexers.ul4',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\ul4.py',
   'PYMODULE'),
  ('pygments.lexers.unicon',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\unicon.py',
   'PYMODULE'),
  ('pygments.lexers.urbi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\urbi.py',
   'PYMODULE'),
  ('pygments.lexers.usd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\usd.py',
   'PYMODULE'),
  ('pygments.lexers.varnish',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\varnish.py',
   'PYMODULE'),
  ('pygments.lexers.verification',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\verification.py',
   'PYMODULE'),
  ('pygments.lexers.web',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\web.py',
   'PYMODULE'),
  ('pygments.lexers.webassembly',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\webassembly.py',
   'PYMODULE'),
  ('pygments.lexers.webidl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\webidl.py',
   'PYMODULE'),
  ('pygments.lexers.webmisc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\webmisc.py',
   'PYMODULE'),
  ('pygments.lexers.wgsl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\wgsl.py',
   'PYMODULE'),
  ('pygments.lexers.whiley',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\whiley.py',
   'PYMODULE'),
  ('pygments.lexers.wowtoc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\wowtoc.py',
   'PYMODULE'),
  ('pygments.lexers.wren',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\wren.py',
   'PYMODULE'),
  ('pygments.lexers.x10',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\x10.py',
   'PYMODULE'),
  ('pygments.lexers.xorg',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\xorg.py',
   'PYMODULE'),
  ('pygments.lexers.yang',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\yang.py',
   'PYMODULE'),
  ('pygments.lexers.zig',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\lexers\\zig.py',
   'PYMODULE'),
  ('pygments.modeline',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\modeline.py',
   'PYMODULE'),
  ('pygments.plugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\plugin.py',
   'PYMODULE'),
  ('pygments.regexopt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\regexopt.py',
   'PYMODULE'),
  ('pygments.scanner',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\scanner.py',
   'PYMODULE'),
  ('pygments.style',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\style.py',
   'PYMODULE'),
  ('pygments.styles',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\__init__.py',
   'PYMODULE'),
  ('pygments.styles.abap',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\abap.py',
   'PYMODULE'),
  ('pygments.styles.algol',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\algol.py',
   'PYMODULE'),
  ('pygments.styles.algol_nu',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\algol_nu.py',
   'PYMODULE'),
  ('pygments.styles.arduino',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\arduino.py',
   'PYMODULE'),
  ('pygments.styles.autumn',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\autumn.py',
   'PYMODULE'),
  ('pygments.styles.borland',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\borland.py',
   'PYMODULE'),
  ('pygments.styles.bw',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\bw.py',
   'PYMODULE'),
  ('pygments.styles.colorful',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\colorful.py',
   'PYMODULE'),
  ('pygments.styles.default',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\default.py',
   'PYMODULE'),
  ('pygments.styles.dracula',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\dracula.py',
   'PYMODULE'),
  ('pygments.styles.emacs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\emacs.py',
   'PYMODULE'),
  ('pygments.styles.friendly',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\friendly.py',
   'PYMODULE'),
  ('pygments.styles.friendly_grayscale',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\friendly_grayscale.py',
   'PYMODULE'),
  ('pygments.styles.fruity',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\fruity.py',
   'PYMODULE'),
  ('pygments.styles.gh_dark',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\gh_dark.py',
   'PYMODULE'),
  ('pygments.styles.gruvbox',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\gruvbox.py',
   'PYMODULE'),
  ('pygments.styles.igor',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\igor.py',
   'PYMODULE'),
  ('pygments.styles.inkpot',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\inkpot.py',
   'PYMODULE'),
  ('pygments.styles.lilypond',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\lilypond.py',
   'PYMODULE'),
  ('pygments.styles.lovelace',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\lovelace.py',
   'PYMODULE'),
  ('pygments.styles.manni',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\manni.py',
   'PYMODULE'),
  ('pygments.styles.material',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\material.py',
   'PYMODULE'),
  ('pygments.styles.monokai',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\monokai.py',
   'PYMODULE'),
  ('pygments.styles.murphy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\murphy.py',
   'PYMODULE'),
  ('pygments.styles.native',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\native.py',
   'PYMODULE'),
  ('pygments.styles.nord',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\nord.py',
   'PYMODULE'),
  ('pygments.styles.onedark',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\onedark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_dark',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\paraiso_dark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_light',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\paraiso_light.py',
   'PYMODULE'),
  ('pygments.styles.pastie',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\pastie.py',
   'PYMODULE'),
  ('pygments.styles.perldoc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\perldoc.py',
   'PYMODULE'),
  ('pygments.styles.rainbow_dash',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\rainbow_dash.py',
   'PYMODULE'),
  ('pygments.styles.rrt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\rrt.py',
   'PYMODULE'),
  ('pygments.styles.sas',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\sas.py',
   'PYMODULE'),
  ('pygments.styles.solarized',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\solarized.py',
   'PYMODULE'),
  ('pygments.styles.staroffice',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\staroffice.py',
   'PYMODULE'),
  ('pygments.styles.stata_dark',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\stata_dark.py',
   'PYMODULE'),
  ('pygments.styles.stata_light',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\stata_light.py',
   'PYMODULE'),
  ('pygments.styles.tango',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\tango.py',
   'PYMODULE'),
  ('pygments.styles.trac',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\trac.py',
   'PYMODULE'),
  ('pygments.styles.vim',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\vim.py',
   'PYMODULE'),
  ('pygments.styles.vs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\vs.py',
   'PYMODULE'),
  ('pygments.styles.xcode',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\xcode.py',
   'PYMODULE'),
  ('pygments.styles.zenburn',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\styles\\zenburn.py',
   'PYMODULE'),
  ('pygments.token',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\token.py',
   'PYMODULE'),
  ('pygments.unistring',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\unistring.py',
   'PYMODULE'),
  ('pygments.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pygments\\util.py',
   'PYMODULE'),
  ('queue', 'C:\\Users\\<USER>\\anaconda3\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Users\\<USER>\\anaconda3\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Users\\<USER>\\anaconda3\\Lib\\random.py', 'PYMODULE'),
  ('referencing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\referencing\\__init__.py',
   'PYMODULE'),
  ('referencing._attrs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\referencing\\_attrs.py',
   'PYMODULE'),
  ('referencing._core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\referencing\\_core.py',
   'PYMODULE'),
  ('referencing.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\referencing\\exceptions.py',
   'PYMODULE'),
  ('referencing.jsonschema',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\referencing\\jsonschema.py',
   'PYMODULE'),
  ('referencing.typing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\referencing\\typing.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rfc3339_validator',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\rfc3339_validator.py',
   'PYMODULE'),
  ('rfc3986_validator',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\rfc3986_validator.py',
   'PYMODULE'),
  ('rlcompleter', 'C:\\Users\\<USER>\\anaconda3\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('rpds',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\rpds\\__init__.py',
   'PYMODULE'),
  ('runpy', 'C:\\Users\\<USER>\\anaconda3\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Users\\<USER>\\anaconda3\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Users\\<USER>\\anaconda3\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'C:\\Users\\<USER>\\anaconda3\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Users\\<USER>\\anaconda3\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Users\\<USER>\\anaconda3\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'C:\\Users\\<USER>\\anaconda3\\Lib\\site.py', 'PYMODULE'),
  ('six', 'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\six.py', 'PYMODULE'),
  ('smtplib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'C:\\Users\\<USER>\\anaconda3\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\socketserver.py',
   'PYMODULE'),
  ('socks',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'C:\\Users\\<USER>\\anaconda3\\Lib\\ssl.py', 'PYMODULE'),
  ('stack_data',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\stack_data\\__init__.py',
   'PYMODULE'),
  ('stack_data.core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\stack_data\\core.py',
   'PYMODULE'),
  ('stack_data.formatting',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\stack_data\\formatting.py',
   'PYMODULE'),
  ('stack_data.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\stack_data\\utils.py',
   'PYMODULE'),
  ('stack_data.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\stack_data\\version.py',
   'PYMODULE'),
  ('statistics', 'C:\\Users\\<USER>\\anaconda3\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'C:\\Users\\<USER>\\anaconda3\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\Users\\<USER>\\anaconda3\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\Users\\<USER>\\anaconda3\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'C:\\Users\\<USER>\\anaconda3\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'C:\\Users\\<USER>\\anaconda3\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Users\\<USER>\\anaconda3\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Users\\<USER>\\anaconda3\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Users\\<USER>\\anaconda3\\Lib\\threading.py', 'PYMODULE'),
  ('threadpoolctl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('timeit', 'C:\\Users\\<USER>\\anaconda3\\Lib\\timeit.py', 'PYMODULE'),
  ('tkinter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('token', 'C:\\Users\\<USER>\\anaconda3\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Users\\<USER>\\anaconda3\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tornado',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\tornado\\__init__.py',
   'PYMODULE'),
  ('tornado.concurrent',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\tornado\\concurrent.py',
   'PYMODULE'),
  ('tornado.escape',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\tornado\\escape.py',
   'PYMODULE'),
  ('tornado.gen',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\tornado\\gen.py',
   'PYMODULE'),
  ('tornado.ioloop',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\tornado\\ioloop.py',
   'PYMODULE'),
  ('tornado.iostream',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\tornado\\iostream.py',
   'PYMODULE'),
  ('tornado.locks',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\tornado\\locks.py',
   'PYMODULE'),
  ('tornado.log',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\tornado\\log.py',
   'PYMODULE'),
  ('tornado.netutil',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\tornado\\netutil.py',
   'PYMODULE'),
  ('tornado.options',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\tornado\\options.py',
   'PYMODULE'),
  ('tornado.platform',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\tornado\\platform\\__init__.py',
   'PYMODULE'),
  ('tornado.platform.asyncio',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\tornado\\platform\\asyncio.py',
   'PYMODULE'),
  ('tornado.process',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\tornado\\process.py',
   'PYMODULE'),
  ('tornado.queues',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\tornado\\queues.py',
   'PYMODULE'),
  ('tornado.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\tornado\\util.py',
   'PYMODULE'),
  ('tracemalloc', 'C:\\Users\\<USER>\\anaconda3\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('traitlets',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\__init__.py',
   'PYMODULE'),
  ('traitlets._version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\_version.py',
   'PYMODULE'),
  ('traitlets.config',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\config\\__init__.py',
   'PYMODULE'),
  ('traitlets.config.application',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\config\\application.py',
   'PYMODULE'),
  ('traitlets.config.argcomplete_config',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\config\\argcomplete_config.py',
   'PYMODULE'),
  ('traitlets.config.configurable',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\config\\configurable.py',
   'PYMODULE'),
  ('traitlets.config.loader',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\config\\loader.py',
   'PYMODULE'),
  ('traitlets.log',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\log.py',
   'PYMODULE'),
  ('traitlets.traitlets',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\traitlets.py',
   'PYMODULE'),
  ('traitlets.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\utils\\__init__.py',
   'PYMODULE'),
  ('traitlets.utils.bunch',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\utils\\bunch.py',
   'PYMODULE'),
  ('traitlets.utils.decorators',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\utils\\decorators.py',
   'PYMODULE'),
  ('traitlets.utils.descriptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\utils\\descriptions.py',
   'PYMODULE'),
  ('traitlets.utils.getargspec',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\utils\\getargspec.py',
   'PYMODULE'),
  ('traitlets.utils.importstring',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\utils\\importstring.py',
   'PYMODULE'),
  ('traitlets.utils.nested_update',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\utils\\nested_update.py',
   'PYMODULE'),
  ('traitlets.utils.sentinel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\utils\\sentinel.py',
   'PYMODULE'),
  ('traitlets.utils.text',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\utils\\text.py',
   'PYMODULE'),
  ('traitlets.utils.warnings',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\traitlets\\utils\\warnings.py',
   'PYMODULE'),
  ('tty', 'C:\\Users\\<USER>\\anaconda3\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\Users\\<USER>\\anaconda3\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('uri_template',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\uri_template\\__init__.py',
   'PYMODULE'),
  ('uri_template.charset',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\uri_template\\charset.py',
   'PYMODULE'),
  ('uri_template.expansions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\uri_template\\expansions.py',
   'PYMODULE'),
  ('uri_template.uritemplate',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\uri_template\\uritemplate.py',
   'PYMODULE'),
  ('uri_template.variable',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\uri_template\\variable.py',
   'PYMODULE'),
  ('urllib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid', 'C:\\Users\\<USER>\\anaconda3\\Lib\\uuid.py', 'PYMODULE'),
  ('wave', 'C:\\Users\\<USER>\\anaconda3\\Lib\\wave.py', 'PYMODULE'),
  ('wcwidth',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wcwidth\\__init__.py',
   'PYMODULE'),
  ('wcwidth.table_wide',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wcwidth\\table_wide.py',
   'PYMODULE'),
  ('wcwidth.table_zero',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wcwidth\\table_zero.py',
   'PYMODULE'),
  ('wcwidth.unicode_versions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wcwidth\\unicode_versions.py',
   'PYMODULE'),
  ('wcwidth.wcwidth',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wcwidth\\wcwidth.py',
   'PYMODULE'),
  ('webbrowser', 'C:\\Users\\<USER>\\anaconda3\\Lib\\webbrowser.py', 'PYMODULE'),
  ('webcolors',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\webcolors\\__init__.py',
   'PYMODULE'),
  ('webcolors._conversion',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\webcolors\\_conversion.py',
   'PYMODULE'),
  ('webcolors._definitions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\webcolors\\_definitions.py',
   'PYMODULE'),
  ('webcolors._html5',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\webcolors\\_html5.py',
   'PYMODULE'),
  ('webcolors._normalization',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\webcolors\\_normalization.py',
   'PYMODULE'),
  ('webcolors._types',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\webcolors\\_types.py',
   'PYMODULE'),
  ('werkzeug',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('wheel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\__init__.py',
   'PYMODULE'),
  ('wheel._setuptools_logging',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\_setuptools_logging.py',
   'PYMODULE'),
  ('wheel.bdist_wheel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\bdist_wheel.py',
   'PYMODULE'),
  ('wheel.cli',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel.metadata',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\util.py',
   'PYMODULE'),
  ('wheel.vendored',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('win32con',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('win_inet_pton',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\win_inet_pton.py',
   'PYMODULE'),
  ('winerror',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml', 'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'C:\\Users\\<USER>\\anaconda3\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc.server',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xmlrpc\\server.py',
   'PYMODULE'),
  ('yaml',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'C:\\Users\\<USER>\\anaconda3\\Lib\\zipimport.py', 'PYMODULE'),
  ('zipp',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp._functools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zipp\\_functools.py',
   'PYMODULE'),
  ('zipp.compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.compat.py313',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zipp\\compat\\py313.py',
   'PYMODULE'),
  ('zipp.glob',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zipp\\glob.py',
   'PYMODULE'),
  ('zmq',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\__init__.py',
   'PYMODULE'),
  ('zmq._future',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\_future.py',
   'PYMODULE'),
  ('zmq._typing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\_typing.py',
   'PYMODULE'),
  ('zmq.asyncio',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\asyncio.py',
   'PYMODULE'),
  ('zmq.backend',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\backend\\__init__.py',
   'PYMODULE'),
  ('zmq.backend.cython',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\backend\\cython\\__init__.py',
   'PYMODULE'),
  ('zmq.backend.select',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\backend\\select.py',
   'PYMODULE'),
  ('zmq.constants',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\constants.py',
   'PYMODULE'),
  ('zmq.error',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\error.py',
   'PYMODULE'),
  ('zmq.eventloop',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\eventloop\\__init__.py',
   'PYMODULE'),
  ('zmq.eventloop.zmqstream',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\eventloop\\zmqstream.py',
   'PYMODULE'),
  ('zmq.green',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\green\\__init__.py',
   'PYMODULE'),
  ('zmq.green.core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\green\\core.py',
   'PYMODULE'),
  ('zmq.green.device',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\green\\device.py',
   'PYMODULE'),
  ('zmq.green.poll',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\green\\poll.py',
   'PYMODULE'),
  ('zmq.sugar',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\sugar\\__init__.py',
   'PYMODULE'),
  ('zmq.sugar.attrsettr',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\sugar\\attrsettr.py',
   'PYMODULE'),
  ('zmq.sugar.context',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\sugar\\context.py',
   'PYMODULE'),
  ('zmq.sugar.frame',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\sugar\\frame.py',
   'PYMODULE'),
  ('zmq.sugar.poll',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\sugar\\poll.py',
   'PYMODULE'),
  ('zmq.sugar.socket',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\sugar\\socket.py',
   'PYMODULE'),
  ('zmq.sugar.stopwatch',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\sugar\\stopwatch.py',
   'PYMODULE'),
  ('zmq.sugar.tracker',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\sugar\\tracker.py',
   'PYMODULE'),
  ('zmq.sugar.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\sugar\\version.py',
   'PYMODULE'),
  ('zmq.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\utils\\__init__.py',
   'PYMODULE'),
  ('zmq.utils.garbage',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\utils\\garbage.py',
   'PYMODULE'),
  ('zmq.utils.interop',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\utils\\interop.py',
   'PYMODULE'),
  ('zmq.utils.jsonapi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zmq\\utils\\jsonapi.py',
   'PYMODULE'),
  ('zstandard',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE')])
