<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>印版尺寸反向计算推理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- 头部 -->
        <header class="bg-primary text-white py-3 mb-4">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="mb-0"><i class="fas fa-calculator me-2"></i>印版尺寸反向计算推理系统</h1>
                        <p class="mb-0 mt-1">专业的印刷行业印版尺寸计算工具</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <small>专业印刷计算工具</small>
                    </div>
                </div>
            </div>
        </header>

        <div class="container">
            <div class="row">
                <!-- 左侧输入面板 -->
                <div class="col-lg-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0"><i class="fas fa-cog me-2"></i>参数设置</h5>
                        </div>
                        <div class="card-body">
                            <form id="calculationForm">
                                <!-- 成品尺寸 -->
                                <div class="mb-3">
                                    <label class="form-label fw-bold">成品尺寸 (mm)</label>
                                    <div class="row">
                                        <div class="col-6">
                                            <input type="number" class="form-control" id="finished_width" 
                                                   placeholder="宽度" step="0.1" min="1" required>
                                            <small class="text-muted">宽度</small>
                                        </div>
                                        <div class="col-6">
                                            <input type="number" class="form-control" id="finished_height" 
                                                   placeholder="高度" step="0.1" min="1" required>
                                            <small class="text-muted">高度</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- 快速选择标准尺寸 -->
                                <div class="mb-3">
                                    <label class="form-label">快速选择标准尺寸</label>
                                    <select class="form-select" id="standard_size">
                                        <option value="">选择标准尺寸...</option>
                                    </select>
                                </div>

                                <!-- 出血位 -->
                                <div class="mb-3">
                                    <label for="bleed" class="form-label">出血位 (mm)</label>
                                    <input type="number" class="form-control" id="bleed" value="3" 
                                           step="0.5" min="0" max="10">
                                    <small class="text-muted">标准出血位为3mm</small>
                                </div>

                                <!-- 安全边距 -->
                                <div class="mb-3">
                                    <label for="safety_margin" class="form-label">安全边距 (mm)</label>
                                    <input type="number" class="form-control" id="safety_margin" value="5" 
                                           step="0.5" min="0" max="20">
                                    <small class="text-muted">推荐5-10mm</small>
                                </div>

                                <!-- 拼版设置 -->
                                <div class="mb-3">
                                    <label class="form-label fw-bold">拼版设置</label>
                                    <div class="row">
                                        <div class="col-6">
                                            <input type="number" class="form-control" id="copies_horizontal" 
                                                   value="1" min="1" max="10">
                                            <small class="text-muted">水平数量</small>
                                        </div>
                                        <div class="col-6">
                                            <input type="number" class="form-control" id="copies_vertical" 
                                                   value="1" min="1" max="10">
                                            <small class="text-muted">垂直数量</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- 咬口方向 -->
                                <div class="mb-3">
                                    <label class="form-label">咬口方向</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="gripper_edge" 
                                               id="gripper_long" value="long" checked>
                                        <label class="form-check-label" for="gripper_long">
                                            长边咬口
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="gripper_edge" 
                                               id="gripper_short" value="short">
                                        <label class="form-check-label" for="gripper_short">
                                            短边咬口
                                        </label>
                                    </div>
                                </div>

                                <!-- 计算按钮 -->
                                <button type="submit" class="btn btn-primary w-100 btn-lg">
                                    <i class="fas fa-calculator me-2"></i>开始计算
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- 印刷机规格参考 -->
                    <div class="card shadow-sm mt-3">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>印刷机规格参考</h6>
                        </div>
                        <div class="card-body p-2">
                            <div id="press_specs" class="small"></div>
                        </div>
                    </div>
                </div>

                <!-- 右侧结果面板 -->
                <div class="col-lg-8">
                    <!-- 计算结果 -->
                    <div class="card shadow-sm mb-3" id="results_card" style="display: none;">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>计算结果</h5>
                        </div>
                        <div class="card-body">
                            <div id="calculation_results"></div>
                        </div>
                    </div>

                    <!-- 最优方案推荐 -->
                    <div class="card shadow-sm mb-3" id="optimal_card" style="display: none;">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>最优方案推荐</h5>
                        </div>
                        <div class="card-body">
                            <div id="optimal_layouts"></div>
                        </div>
                    </div>

                    <!-- 成本分析 -->
                    <div class="card shadow-sm mb-3" id="cost_card" style="display: none;">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0"><i class="fas fa-dollar-sign me-2"></i>成本分析</h5>
                        </div>
                        <div class="card-body">
                            <div id="cost_analysis"></div>
                        </div>
                    </div>

                    <!-- 可视化图表 -->
                    <div class="card shadow-sm" id="visualization_card" style="display: none;">
                        <div class="card-header bg-dark text-white">
                            <h5 class="mb-0"><i class="fas fa-eye me-2"></i>尺寸可视化</h5>
                        </div>
                        <div class="card-body">
                            <div id="size_visualization" class="text-center"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loading_overlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">计算中...</span>
            </div>
            <p class="mt-2">正在计算中，请稍候...</p>
        </div>
    </div>

    <!-- 错误提示模态框 -->
    <div class="modal fade" id="errorModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">错误提示</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="error_message"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
